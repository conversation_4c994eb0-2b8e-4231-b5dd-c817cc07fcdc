"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { NewVehicleLoadingPanel } from "@/components/new-vehicle-loading-panel"
import { NewVehicleReceivingPanel } from "@/components/new-vehicle-receiving-panel"

export function LoadReceiveManagement() {
  return (
    <div className="space-y-4">
      <Tabs defaultValue="load" className="space-y-4">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="load" className="text-sm">Load Parcels</TabsTrigger>
          <TabsTrigger value="receive" className="text-sm">Receive Parcels</TabsTrigger>
        </TabsList>
        <TabsContent value="load" className="space-y-4">
          <NewVehicleLoadingPanel />
        </TabsContent>
        <TabsContent value="receive" className="space-y-4">
          <NewVehicleReceivingPanel />
        </TabsContent>
      </Tabs>
    </div>
  )
}
