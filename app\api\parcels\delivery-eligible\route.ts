import { NextRequest, NextResponse } from "next/server";
import { supabase } from "@/lib/supabase";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";

// GET /api/parcels/delivery-eligible
export async function GET(request: NextRequest) {
  try {
    const routeHandlerClient = createRouteHandlerClient({ cookies });
    const { data: { session } } = await routeHandlerClient.auth.getSession();
    
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { data: { user } } = await routeHandlerClient.auth.getUser();

    // Get user's branch information
    const { data: userData, error: userError } = await supabase
      .from("users")
      .select("branch_id")
      .eq("auth_id", user?.id)
      .single();

    if (userError || !userData) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get("page") || "1");
    const pageSize = parseInt(url.searchParams.get("pageSize") || "20");
    const search = url.searchParams.get("search") || "";

    // Calculate pagination
    const from = (page - 1) * pageSize;
    const to = from + pageSize - 1;

    // Build query for delivery-eligible parcels at user's branch
    let query = supabase
      .from("delivery_eligible_parcels")
      .select("*", { count: "exact" })
      .eq("delivery_branch_id", userData.branch_id)
      .eq("ready_for_delivery", true)
      .order("booking_datetime", { ascending: false })
      .range(from, to);

    // Add search filter if provided
    if (search) {
      query = query.or(`lr_number.ilike.%${search}%,sender_name.ilike.%${search}%,recipient_name.ilike.%${search}%`);
    }

    const { data: parcels, error: parcelsError, count } = await query;

    if (parcelsError) {
      console.error("Error fetching delivery-eligible parcels:", parcelsError);
      return NextResponse.json({ error: "Failed to fetch parcels" }, { status: 500 });
    }

    return NextResponse.json({
      parcels: parcels || [],
      pagination: {
        page,
        pageSize,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / pageSize)
      }
    });

  } catch (error: any) {
    console.error("Error in GET /api/parcels/delivery-eligible:", error);
    return NextResponse.json({ error: "Internal Server Error" }, { status: 500 });
  }
}
