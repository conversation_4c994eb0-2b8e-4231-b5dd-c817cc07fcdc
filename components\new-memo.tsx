"use client"

import { useState, useEffect } from "react"
import { z } from "zod"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useToast } from "@/hooks/use-toast"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog"
import { Plus, Minus, AlertCircle, CheckCircle2, Loader2, <PERSON>, Copy } from "lucide-react"
import { <PERSON><PERSON>, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"

const formSchema = z.object({
  vehicleId: z.string().optional(),
  vehicleNumber: z.string().min(1, "Vehicle number is required"),
  drivers: z.array(z.object({
    id: z.string().optional(),
    driverNumber: z.string().min(1, "Driver number is required")
  })).min(1, "At least one driver is required").max(2, "Maximum two drivers allowed"),
  fromBranchId: z.string().min(1, "From branch is required"),
  toBranchId: z.string().min(1, "To branch is required"),
  viaPoints: z.array(z.string()).optional(),
})

export function NewMemo() {
  const { toast } = useToast()
  const [cities, setCities] = useState<any[]>([])
  const [branches, setBranches] = useState<any[]>([])
  const [filteredBranches, setFilteredBranches] = useState<any[]>([])
  const [filteredToBranches, setFilteredToBranches] = useState<any[]>([])
  const [vehicles, setVehicles] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [showSuccessDialog, setShowSuccessDialog] = useState(false)
  const [generatedMemoNumber, setGeneratedMemoNumber] = useState("")
  const [vehicleValidation, setVehicleValidation] = useState<any>(null)
  const [driverValidations, setDriverValidations] = useState<any[]>([])
  const [isValidatingVehicle, setIsValidatingVehicle] = useState(false)
  const [isValidatingDrivers, setIsValidatingDrivers] = useState(false)
  const [selectedViaPoints, setSelectedViaPoints] = useState<string[]>([])
  const [selectedFromCity, setSelectedFromCity] = useState<string>("")
  const [selectedToCity, setSelectedToCity] = useState<string>("")
  const [debugMode, setDebugMode] = useState<boolean>(false)
  const [showDriversDialog, setShowDriversDialog] = useState<boolean>(false)
  const [availableDrivers, setAvailableDrivers] = useState<any[]>([])

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      vehicleNumber: "",
      drivers: [{ driverNumber: "" }],
      fromBranchId: "",
      toBranchId: "",
      viaPoints: [],
    },
  })

  // Fetch cities, branches and vehicles on component mount
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true)
      try {
        // Fetch user's branch ID
        console.log('Fetching user branch ID...')
        const userBranchResponse = await fetch(`${window.location.origin}/api/auth/user-branch`)
        const userBranchData = await userBranchResponse.json()
        console.log('User branch response:', userBranchData)

        // Get user branch ID (use localStorage if not available from API)
        let userBranchId = userBranchData.branch_id?.toString()
        if (!userBranchId) {
          // Try to get from localStorage as fallback
          const localBranchId = localStorage.getItem('user_branch_id')
          if (localBranchId) {
            userBranchId = localBranchId
            console.log('Using branch ID from localStorage:', userBranchId)
          } else {
            // Only use hardcoded value as last resort
            userBranchId = "1" // Fallback for testing
            console.log('Using fallback branch ID:', userBranchId)
          }
        } else {
          console.log('Using branch ID from API:', userBranchId)
          // Store in localStorage for future use
          try {
            localStorage.setItem('user_branch_id', userBranchId)
          } catch (e) {
            console.warn('Failed to store branch ID in localStorage:', e)
          }
        }

        // Fetch cities
        const citiesResponse = await fetch(`${window.location.origin}/api/cities`)
        const citiesData = await citiesResponse.json()

        // Fetch all branches
        const branchesResponse = await fetch(`${window.location.origin}/api/branches?status=Active`)
        const branchesData = await branchesResponse.json()

        // Fetch vehicles
        const vehiclesResponse = await fetch(`${window.location.origin}/api/vehicles?status=Active`)
        const vehiclesData = await vehiclesResponse.json()

        setCities(citiesData || [])
        setBranches(branchesData.branches || [])
        setFilteredBranches(branchesData.branches || [])
        setFilteredToBranches(branchesData.branches || [])
        setVehicles(vehiclesData.vehicles || [])

        // Find the branch to get its city
        console.log('Looking for branch with ID:', userBranchId)
        console.log('Available branches:', branchesData.branches)

        // Make sure we're comparing the same types (string to string)
        const userBranch = branchesData.branches?.find(b =>
          b.branch_id.toString() === userBranchId.toString()
        )
        console.log('User branch found:', userBranch)

        if (userBranch && userBranch.city_id) {
          console.log('Setting from city to:', userBranch.city_id)
          // Set the from city
          setSelectedFromCity(userBranch.city_id.toString())

          // Set the from branch in the form
          console.log('Setting from branch to:', userBranchId)
          form.setValue("fromBranchId", userBranchId)

          // Trigger a fetch of branches for this city
          try {
            const response = await fetch(`${window.location.origin}/api/branches/by-city?city_id=${userBranch.city_id}&status=Active`)
            const data = await response.json()
            setFilteredBranches(data.branches || [])
          } catch (error: any) {
            console.error('Error fetching branches by city:', error)
          }
        } else {
          console.error('Could not find user branch or city_id is missing:', userBranch)

          // Fallback: Directly fetch the branch information
          try {
            console.log('Attempting to fetch branch directly with ID:', userBranchId)
            const branchResponse = await fetch(`${window.location.origin}/api/branches/${userBranchId}`)

            if (branchResponse.ok) {
              const branchData = await branchResponse.json()
              console.log('Direct branch fetch result:', branchData)

              if (branchData.branch && branchData.branch.city_id) {
                console.log('Setting from city to (from direct fetch):', branchData.branch.city_id)
                // Set the from city
                setSelectedFromCity(branchData.branch.city_id.toString())

                // Set the from branch in the form
                console.log('Setting from branch to (from direct fetch):', userBranchId)
                form.setValue("fromBranchId", userBranchId)

                // Add this branch to the branches list if it's not there
                // Make sure we're using a consistent string conversion for branch_id
                const branchIdStr = userBranchId.toString();
                if (!branches.some(b => b.branch_id.toString() === branchIdStr)) {
                  // Ensure the branch_id is consistently stored as a number
                  const branchToAdd = {
                    ...branchData.branch,
                    branch_id: parseInt(branchIdStr)
                  };
                  setBranches(prev => [...prev, branchToAdd])
                  setFilteredBranches(prev => [...prev, branchToAdd])
                }
              }
            } else {
              console.error('Failed to fetch branch directly:', await branchResponse.text())
            }
          } catch (branchError: any) {
            console.error('Error fetching branch directly:', branchError)
          }
        }
      } catch (error: any) {
        console.error('Error fetching data:', error)
        toast({
          title: "Error",
          description: "Failed to load data. Please try again.",
          variant: "destructive",
        })
      } finally {
        setIsLoading(false)
      }
    }

    fetchData()
    // We intentionally exclude branches from dependencies to avoid re-fetching
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [toast, form])

  // Filter branches when from city changes
  useEffect(() => {
    if (selectedFromCity) {
      const fetchBranchesByCity = async () => {
        try {
          const response = await fetch(`${window.location.origin}/api/branches/by-city?city_id=${selectedFromCity}&status=Active`)
          const data = await response.json()
          setFilteredBranches(data.branches || [])

          // Reset the selected branch if it's not in the filtered list
          const currentBranchId = form.getValues("fromBranchId")
          const branchExists = data.branches?.some((b: any) => b.branch_id.toString() === currentBranchId)

          if (currentBranchId && !branchExists) {
            form.setValue("fromBranchId", "")
          }
        } catch (error: any) {
          console.error('Error fetching branches by city:', error)
        }
      }

      fetchBranchesByCity()
    } else {
      setFilteredBranches(branches)
    }
  }, [selectedFromCity, branches, form])

  // Filter branches when to city changes
  useEffect(() => {
    if (selectedToCity) {
      const fetchBranchesByCity = async () => {
        try {
          const response = await fetch(`${window.location.origin}/api/branches/by-city?city_id=${selectedToCity}&status=Active`)
          const data = await response.json()
          setFilteredToBranches(data.branches || [])

          // Get the current from and to branch IDs
          const fromBranchId = form.getValues("fromBranchId")
          const currentToBranchId = form.getValues("toBranchId")

          // Check if the current to branch exists in the filtered list
          const branchExists = data.branches?.some((b: any) => b.branch_id.toString() === currentToBranchId)

          // Reset the to branch if:
          // 1. It doesn't exist in the filtered list, OR
          // 2. It's the same as the from branch
          if (currentToBranchId && (!branchExists || currentToBranchId === fromBranchId)) {
            form.setValue("toBranchId", "")
          }
        } catch (error: any) {
          console.error('Error fetching branches by city:', error)
        }
      }

      fetchBranchesByCity()
    } else {
      setFilteredToBranches(branches)
    }
  }, [selectedToCity, branches, form])

  // Add driver function
  const addDriver = () => {
    const currentDrivers = form.getValues("drivers")
    if (currentDrivers.length < 2) {
      form.setValue("drivers", [
        ...currentDrivers,
        { driverNumber: "" }
      ])
    }
  }

  // Remove driver function
  const removeDriver = (index: number) => {
    const currentDrivers = form.getValues("drivers")
    if (currentDrivers.length > 1) {
      form.setValue("drivers", currentDrivers.filter((_, i) => i !== index))
      // Also remove the validation for this driver
      setDriverValidations(prev => prev.filter((_, i) => i !== index))
    }
  }

  // Validate vehicle function
  const validateVehicle = async (vehicleNumber: string) => {
    if (!vehicleNumber) return

    setIsValidatingVehicle(true)
    try {
      const response = await fetch(`${window.location.origin}/api/vehicles/validation`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ registration_number: vehicleNumber }),
      })

      const data = await response.json()

      if (response.ok) {
        setVehicleValidation(data)
        if (data.vehicle_id) {
          form.setValue("vehicleId", data.vehicle_id.toString())
        }

        if (!data.valid) {
          toast({
            title: "Vehicle Validation Failed",
            description: data.message,
            variant: "destructive",
          })
        } else {
          toast({
            title: "Vehicle Validated",
            description: "All vehicle documents are valid.",
          })
        }
      } else {
        setVehicleValidation({ valid: false, message: data.error || "Vehicle not found" })
        toast({
          title: "Error",
          description: data.error || "Failed to validate vehicle",
          variant: "destructive",
        })
      }
    } catch (error: any) {
      console.error('Error validating vehicle:', error)
      setVehicleValidation({ valid: false, message: "Failed to validate vehicle" })
      toast({
        title: "Error",
        description: "Failed to validate vehicle. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsValidatingVehicle(false)
    }
  }

  // Validate driver function
  const validateDriver = async (driverNumber: string, index: number) => {
    if (!driverNumber) return

    setIsValidatingDrivers(true)
    try {
      // Get vehicle ID if available
      const vehicleId = vehicleValidation?.vehicle_id;

      console.log('Validating driver with:', { driverNumber, vehicleId });

      try {
        // First check if the driver exists
        const checkResponse = await fetch(`${window.location.origin}/api/drivers/check?search=${encodeURIComponent(driverNumber)}`);
        const checkData = await checkResponse.json();

        if (!checkData.exists) {
          // Driver doesn't exist, show detailed error
          const newValidations = [...driverValidations];
          newValidations[index] = {
            valid: false,
            message: `No driver found with ID or phone number: ${driverNumber}`,
            searchDetails: {
              field: 'driver_number or contact_number',
              value: driverNumber
            }
          };
          setDriverValidations(newValidations);

          toast({
            title: "Driver Not Found",
            description: `No driver found with ID or phone number: ${driverNumber}`,
            variant: "destructive",
          });

          setIsValidatingDrivers(false);
          return;
        }

        // Driver exists, proceed with validation
        const response = await fetch(`${window.location.origin}/api/driver-validation`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            driver_number: driverNumber,
            vehicle_id: vehicleId // Pass vehicle ID for compatibility check
          }),
        });

        // Check if the response is ok
        if (!response.ok) {
          throw new Error(`API returned status ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();

        // Check if the data has an error property
        if (data.error) {
          throw new Error(data.error);
        }

        // If we get here, we have a successful response with data
        const newValidations = [...driverValidations];
        newValidations[index] = data;
        setDriverValidations(newValidations);

        // Update the driver ID in the form
        if (data.driver_id) {
          const drivers = form.getValues("drivers");
          drivers[index].id = data.driver_id.toString();
          form.setValue("drivers", drivers);
        }

        if (!data.valid) {
          toast({
            title: "Driver Validation Failed",
            description: data.message,
            variant: "destructive",
          });
        } else {
          toast({
            title: "Driver Validated",
            description: `Driver ${data.driver?.name} is eligible for this memo.`,
          });
        }
      } catch (validationError: any) {
        console.error('Error during driver validation:', validationError);

        // Create a validation error entry
        const newValidations = [...driverValidations];

        // Try to get more details from the error
        let errorDetails = null;
        if (validationError instanceof Error) {
          errorDetails = validationError.stack;
        }

        // Try to parse the error response if it's a fetch error
        let responseDetails = null;
        try {
          if (validationError.response) {
            responseDetails = await validationError.response.text();
          }
        } catch (e: any) {
          console.error("Failed to get response details:", e);
        }

        newValidations[index] = {
          valid: false,
          message: `Validation error: ${validationError.message || 'Unknown error'}`,
          error: true,
          details: errorDetails,
          responseDetails: responseDetails
        };
        setDriverValidations(newValidations);

        toast({
          title: "Validation Error",
          description: `Error validating driver: ${validationError.message || 'Unknown error'}`,
          variant: "destructive",
        });

        setIsValidatingDrivers(false);
        return;
      }
    } catch (error: any) {
      console.error('Error validating driver:', error)

      // Try a direct test to see if the API is accessible
      try {
        const directTest = await fetch(`${window.location.origin}/api/drivers/test-validation`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ driver_number: driverNumber }),
        });
        console.log('Test validation results:', await directTest.json());
      } catch (testError: any) {
        console.error('Test validation failed:', testError);
      }

      const newValidations = [...driverValidations]
      newValidations[index] = {
        valid: false,
        message: `Failed to validate driver: ${error instanceof Error ? error.message : 'Unknown error'}`,
        error: true
      }
      setDriverValidations(newValidations)

      toast({
        title: "Validation Error",
        description: "Failed to validate driver. Check console for details.",
        variant: "destructive",
      })
    } finally {
      setIsValidatingDrivers(false)
    }
  }

  // Handle via point selection
  const handleViaPointChange = (branchId: string) => {
    const currentViaPoints = [...selectedViaPoints]
    const index = currentViaPoints.indexOf(branchId)

    if (index === -1) {
      // Add the branch ID to via points
      currentViaPoints.push(branchId)
    } else {
      // Remove the branch ID from via points
      currentViaPoints.splice(index, 1)
    }

    setSelectedViaPoints(currentViaPoints)
    form.setValue("viaPoints", currentViaPoints)
  }

  // Submit function
  async function onSubmit(values: z.infer<typeof formSchema>) {
    // Check if vehicle and at least one driver are validated
    if (!vehicleValidation?.valid) {
      toast({
        title: "Validation Required",
        description: "Please validate the vehicle before creating a memo.",
        variant: "destructive",
      })
      return
    }

    if (!driverValidations.length || !driverValidations.some(v => v?.valid)) {
      toast({
        title: "Validation Required",
        description: "Please validate at least one driver before creating a memo.",
        variant: "destructive",
      })
      return
    }

    // Check that From Branch and To Branch are not the same
    if (values.fromBranchId === values.toBranchId) {
      toast({
        title: "Invalid Selection",
        description: "From Branch and To Branch cannot be the same.",
        variant: "destructive",
      })
      return
    }

    setIsSubmitting(true)

    try {
      // Prepare driver IDs array
      const driverIds = values.drivers
        .filter((d, i) => driverValidations[i]?.valid && d.id)
        .map(d => parseInt(d.id as string))

      // Get current user ID
      const userResponse = await fetch(`${window.location.origin}/api/auth/session`)
      const userData = await userResponse.json()
      const userId = userData?.user?.id

      // Create memo
      const response = await fetch(`${window.location.origin}/api/memos`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          vehicle_id: parseInt(values.vehicleId as string),
          driver_ids: driverIds,
          from_branch_id: parseInt(values.fromBranchId),
          to_branch_id: parseInt(values.toBranchId),
          via_points: values.viaPoints?.map(id => parseInt(id)) || [],
          vehicle_validation: vehicleValidation.validation,
          driver_validation: driverValidations.find(v => v?.valid)?.validation || {},
          created_by: userId
        }),
      })

      const data = await response.json()

      if (response.ok) {
        setGeneratedMemoNumber(data.memo.memo_number)
        setShowSuccessDialog(true)
      } else {
        toast({
          title: "Error",
          description: data.error || "Failed to create memo",
          variant: "destructive",
        })
      }
    } catch (error: any) {
      console.error('Error creating memo:', error)
      toast({
        title: "Error",
        description: "Failed to create memo. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  function handleDialogClose() {
    setShowSuccessDialog(false)
    form.reset()
    setVehicleValidation(null)
    setDriverValidations([])
    setSelectedViaPoints([])
  }

  // Function to fetch and show available drivers
  const fetchAvailableDrivers = async () => {
    try {
      const response = await fetch(`${window.location.origin}/api/drivers/list`);
      const data = await response.json();

      if (response.ok) {
        setAvailableDrivers(data.drivers || []);
        setShowDriversDialog(true);
      } else {
        toast({
          title: "Error",
          description: data.error || "Failed to fetch drivers",
          variant: "destructive",
        });
      }
    } catch (error: any) {
      console.error('Error fetching drivers:', error);
      toast({
        title: "Error",
        description: "Failed to fetch drivers. Please try again.",
        variant: "destructive",
      });
    }
  }

  return (
    <>
      {isLoading ? (
        <div className="flex items-center justify-center h-[400px]">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <span className="ml-2">Loading...</span>
        </div>
      ) : (
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Vehicle Details Card */}
            <Card>
              <CardHeader>
                <CardTitle>Vehicle Details</CardTitle>
                <CardDescription>Enter and validate the vehicle information</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex gap-2">
                  <div className="flex-1">
                    <FormField
                      control={form.control}
                      name="vehicleNumber"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Vehicle Number</FormLabel>
                          <div className="flex gap-2">
                            <FormControl>
                              <Input placeholder="TN01BR5678" {...field} />
                            </FormControl>
                            <Button
                              type="button"
                              onClick={() => validateVehicle(field.value)}
                              disabled={isValidatingVehicle || !field.value}
                            >
                              {isValidatingVehicle ? (
                                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                              ) : (
                                "Validate"
                              )}
                            </Button>
                          </div>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>

                {vehicleValidation && (
                  <Alert variant={vehicleValidation.valid ? "default" : "destructive"}>
                    <div className="flex items-center">
                      {vehicleValidation.valid ? (
                        <CheckCircle2 className="h-4 w-4 mr-2" />
                      ) : (
                        <AlertCircle className="h-4 w-4 mr-2" />
                      )}
                      <AlertTitle>
                        {vehicleValidation.valid ? "Vehicle Validated" : "Validation Failed"}
                      </AlertTitle>
                    </div>
                    <AlertDescription className="mt-2">
                      {vehicleValidation.message}

                      {vehicleValidation.validation && (
                        <div className="mt-2 grid grid-cols-2 gap-2">
                          <div className="flex items-center">
                            <Badge variant={vehicleValidation.validation.fc_valid ? "default" : "destructive"} className="mr-2">
                              {vehicleValidation.validation.fc_valid ? "Valid" : "Invalid"}
                            </Badge>
                            <span>FC Certificate</span>
                          </div>
                          <div className="flex items-center">
                            <Badge variant={vehicleValidation.validation.insurance_valid ? "default" : "destructive"} className="mr-2">
                              {vehicleValidation.validation.insurance_valid ? "Valid" : "Invalid"}
                            </Badge>
                            <span>Insurance</span>
                          </div>
                          <div className="flex items-center">
                            <Badge variant={vehicleValidation.validation.permit_valid ? "default" : "destructive"} className="mr-2">
                              {vehicleValidation.validation.permit_valid ? "Valid" : "Invalid"}
                            </Badge>
                            <span>Permit</span>
                          </div>
                          <div className="flex items-center">
                            <Badge variant={vehicleValidation.validation.tax_valid ? "default" : "destructive"} className="mr-2">
                              {vehicleValidation.validation.tax_valid ? "Valid" : "Invalid"}
                            </Badge>
                            <span>Tax</span>
                          </div>
                        </div>
                      )}
                    </AlertDescription>
                  </Alert>
                )}
              </CardContent>
            </Card>

            {/* Driver Details Card */}
            <Card>
              <CardHeader>
                <CardTitle>Driver Details</CardTitle>
                <CardDescription>Enter and validate driver information (up to two drivers)</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <h4 className="text-sm font-medium">Drivers</h4>
                  <div className="flex gap-2">
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={fetchAvailableDrivers}
                    >
                      <Users className="h-4 w-4 mr-2" />
                      View Available Drivers
                    </Button>
                    {debugMode && (
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={async () => {
                          try {
                            const driverNumber = form.getValues("drivers")[0]?.driverNumber || "";
                            if (!driverNumber) {
                              toast({
                                title: "Error",
                                description: "Please enter a driver number or phone first",
                                variant: "destructive",
                              });
                              return;
                            }

                            const response = await fetch(`/api/drivers/debug?phone=${encodeURIComponent(driverNumber)}`);
                            const data = await response.json();
                            console.log("Debug results:", data);

                            toast({
                              title: "Debug Info",
                              description: "Check console for detailed debug information",
                            });
                          } catch (error: any) {
                            console.error("Debug error:", error);
                            toast({
                              title: "Debug Error",
                              description: error.message,
                              variant: "destructive",
                            });
                          }
                        }}
                      >
                        Debug Driver
                      </Button>
                    )}
                    {form.watch("drivers").length < 2 && (
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={addDriver}
                      >
                        <Plus className="h-4 w-4 mr-2" />
                        Add Driver
                      </Button>
                    )}
                  </div>
                </div>

                {form.watch("drivers").map((driver, index) => (
                  <div key={index} className="space-y-4 border p-4 rounded-md">
                    <div className="flex items-center justify-between">
                      <FormLabel>Driver {index + 1}</FormLabel>
                      {index > 0 && (
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => removeDriver(index)}
                        >
                          <Minus className="h-4 w-4 mr-2" />
                          Remove
                        </Button>
                      )}
                    </div>
                    <FormField
                      control={form.control}
                      name={`drivers.${index}.driverNumber`}
                      render={({ field }) => (
                        <FormItem>
                          <div className="space-y-2">
                            <div className="flex gap-2">
                              <FormControl>
                                <Input placeholder="Enter driver ID or mobile number" {...field} />
                              </FormControl>
                              <div className="flex gap-1">
                                <Button
                                  type="button"
                                  onClick={() => validateDriver(field.value, index)}
                                  disabled={isValidatingDrivers || !field.value}
                                >
                                  {isValidatingDrivers ? (
                                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                                  ) : (
                                    "Validate"
                                  )}
                                </Button>
                                {debugMode && (
                                  <Button
                                    type="button"
                                    variant="outline"
                                    size="sm"
                                    onClick={async () => {
                                      try {
                                        const response = await fetch('/api/drivers/test-validation', {
                                          method: 'POST',
                                          headers: {
                                            'Content-Type': 'application/json',
                                          },
                                          body: JSON.stringify({
                                            driver_number: field.value
                                          }),
                                        });
                                        const data = await response.json();
                                        console.log("Test validation results:", data);

                                        toast({
                                          title: "Test Results",
                                          description: "Check console for detailed test results",
                                        });
                                      } catch (error: any) {
                                        console.error("Test validation error:", error);
                                        toast({
                                          title: "Test Error",
                                          description: error.message,
                                          variant: "destructive",
                                        });
                                      }
                                    }}
                                  >
                                    Test
                                  </Button>
                                )}
                              </div>
                            </div>
                            <div className="text-xs text-muted-foreground">
                              Enter either the driver's ID number or mobile phone number as registered in the system
                            </div>
                          </div>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {driverValidations[index] && (
                      <Alert variant={driverValidations[index].valid ? "default" : "destructive"}>
                        <div className="flex items-center">
                          {driverValidations[index].valid ? (
                            <CheckCircle2 className="h-4 w-4 mr-2" />
                          ) : (
                            <AlertCircle className="h-4 w-4 mr-2" />
                          )}
                          <AlertTitle>
                            {driverValidations[index].valid ? "Driver Validated" : "Validation Failed"}
                          </AlertTitle>
                        </div>
                        <AlertDescription className="mt-2">
                          {driverValidations[index].message}

                          {driverValidations[index].driver && (
                            <div className="mt-2">
                              <p>Name: {driverValidations[index].driver.name}</p>
                              <p>Contact: {driverValidations[index].driver.contact_number}</p>
                              {driverValidations[index].driver.license_type && (
                                <p>License Type: {driverValidations[index].driver.license_type}</p>
                              )}
                              {driverValidations[index].driver.license_expiry_date && (
                                <p>License Expiry: {new Date(driverValidations[index].driver.license_expiry_date).toLocaleDateString()}</p>
                              )}
                            </div>
                          )}

                          {/* Show search details if driver not found */}
                          {driverValidations[index].searchDetails && (
                            <Alert variant="destructive" className="mt-2 py-2">
                              <AlertCircle className="h-4 w-4 mr-2" />
                              <AlertTitle className="text-sm">Driver Not Found</AlertTitle>
                              <AlertDescription className="text-xs">
                                No driver found with {driverValidations[index].searchDetails.field}: {driverValidations[index].searchDetails.value}
                                <div className="mt-1">
                                  Please check that you entered the correct driver ID or mobile number as registered in the system.
                                </div>
                                {debugMode && (
                                  <div className="mt-2 p-2 bg-muted rounded-sm text-xs">
                                    <p className="font-semibold">Debug Info:</p>
                                    <p>Search value: {driverValidations[index].searchDetails.value}</p>
                                    <p>Search field: {driverValidations[index].searchDetails.field}</p>
                                    <p>Error: {driverValidations[index].message}</p>
                                    {driverValidations[index].details && (
                                      <div className="mt-1">
                                        <p className="font-semibold">Error Details:</p>
                                        <pre className="text-xs overflow-auto max-h-[100px] bg-black/10 p-1 rounded">
                                          {driverValidations[index].details}
                                        </pre>
                                      </div>
                                    )}
                                    {driverValidations[index].responseDetails && (
                                      <div className="mt-1">
                                        <p className="font-semibold">Response Details:</p>
                                        <pre className="text-xs overflow-auto max-h-[100px] bg-black/10 p-1 rounded">
                                          {driverValidations[index].responseDetails}
                                        </pre>
                                      </div>
                                    )}
                                    <div className="flex gap-2 mt-2">
                                      <Button
                                        type="button"
                                        variant="outline"
                                        size="sm"
                                        onClick={async () => {
                                          try {
                                            const response = await fetch(`/api/drivers/debug?phone=${encodeURIComponent(driverValidations[index].searchDetails.value)}`);
                                            const data = await response.json();
                                            console.log("Debug results:", data);

                                            toast({
                                              title: "Debug Info",
                                              description: "Check console for detailed debug information",
                                            });
                                          } catch (error: any) {
                                            console.error("Debug error:", error);
                                          }
                                        }}
                                      >
                                        Run Debug
                                      </Button>
                                      <Button
                                        type="button"
                                        variant="outline"
                                        size="sm"
                                        onClick={async () => {
                                          try {
                                            const response = await fetch('/api/drivers/test-validation', {
                                              method: 'POST',
                                              headers: {
                                                'Content-Type': 'application/json',
                                              },
                                              body: JSON.stringify({
                                                driver_number: driverValidations[index].searchDetails.value
                                              }),
                                            });
                                            const data = await response.json();
                                            console.log("Test validation results:", data);

                                            toast({
                                              title: "Test Results",
                                              description: "Check console for detailed test results",
                                            });
                                          } catch (error: any) {
                                            console.error("Test validation error:", error);
                                            toast({
                                              title: "Test Error",
                                              description: error.message,
                                              variant: "destructive",
                                            });
                                          }
                                        }}
                                      >
                                        Test Validation
                                      </Button>
                                    </div>
                                  </div>
                                )}
                              </AlertDescription>
                            </Alert>
                          )}

                          {driverValidations[index].validation && (
                            <div className="mt-2 space-y-3">
                              <div className="grid grid-cols-2 gap-2">
                                <div className="flex items-center">
                                  <Badge variant={driverValidations[index].validation.dl_valid ? "default" : "destructive"} className="mr-2">
                                    {driverValidations[index].validation.dl_valid ? "Valid" : "Invalid"}
                                  </Badge>
                                  <span>Driver's License</span>
                                </div>
                                {driverValidations[index].validation.vehicle_type_valid !== undefined && (
                                  <div className="flex items-center">
                                    <Badge variant={driverValidations[index].validation.vehicle_type_valid ? "default" : "destructive"} className="mr-2">
                                      {driverValidations[index].validation.vehicle_type_valid ? "Compatible" : "Incompatible"}
                                    </Badge>
                                    <span>Vehicle Compatibility</span>
                                  </div>
                                )}
                                {driverValidations[index].validation.eligibility_valid !== undefined && (
                                  <div className="flex items-center">
                                    <Badge variant={driverValidations[index].validation.eligibility_valid ? "default" : "destructive"} className="mr-2">
                                      {driverValidations[index].validation.eligibility_valid ? "Eligible" : "Not Eligible"}
                                    </Badge>
                                    <span>Overall Eligibility</span>
                                  </div>
                                )}
                              </div>

                              {!driverValidations[index].validation.vehicle_type_valid && (
                                <Alert variant="destructive" className="py-2">
                                  <AlertCircle className="h-4 w-4 mr-2" />
                                  <AlertTitle className="text-sm">Incompatible License</AlertTitle>
                                  <AlertDescription className="text-xs">
                                    The driver's license type ({driverValidations[index].driver?.license_type || 'Unknown'})
                                    is not compatible with the selected vehicle type.
                                  </AlertDescription>
                                </Alert>
                              )}

                              {!driverValidations[index].validation.dl_valid && (
                                <Alert variant="destructive" className="py-2">
                                  <AlertCircle className="h-4 w-4 mr-2" />
                                  <AlertTitle className="text-sm">Expired License</AlertTitle>
                                  <AlertDescription className="text-xs">
                                    The driver's license has expired or is invalid.
                                  </AlertDescription>
                                </Alert>
                              )}

                              {driverValidations[index].active_memos && driverValidations[index].active_memos.length > 0 && (
                                <Alert variant="destructive" className="py-2">
                                  <AlertCircle className="h-4 w-4 mr-2" />
                                  <AlertTitle className="text-sm">Already Assigned</AlertTitle>
                                  <AlertDescription className="text-xs">
                                    This driver is already assigned to active memo(s).
                                  </AlertDescription>
                                </Alert>
                              )}
                            </div>
                          )}

                          {driverValidations[index].active_memos && driverValidations[index].active_memos.length > 0 && (
                            <div className="mt-2">
                              <p className="font-medium">Active in memos:</p>
                              <ul className="list-disc pl-5">
                                {driverValidations[index].active_memos.map((memo: any) => (
                                  <li key={memo.memo_id}>{memo.memo_number} ({memo.status})</li>
                                ))}
                              </ul>
                            </div>
                          )}
                        </AlertDescription>
                      </Alert>
                    )}
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* Route Information Card */}
            <Card>
              <CardHeader>
                <CardTitle>Route Information</CardTitle>
                <CardDescription>Select the source, destination, and via points</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* From City and Branch Selection */}
                  <div className="space-y-4">
                    <FormItem>
                      <FormLabel>From City</FormLabel>
                      <div className="relative">
                        {/* Use a static display instead of a Select component for disabled fields */}
                        <div className="flex h-10 w-full items-center justify-between rounded-md border border-input bg-muted px-3 py-2 text-sm">
                          {selectedFromCity && cities.find(c => c.city_id.toString() === selectedFromCity) ? (
                            <span>
                              {cities.find(c => c.city_id.toString() === selectedFromCity)?.name}
                              {cities.find(c => c.city_id.toString() === selectedFromCity)?.state ?
                                `, ${cities.find(c => c.city_id.toString() === selectedFromCity)?.state}` : ''}
                            </span>
                          ) : (
                            <span className="text-muted-foreground">Your City</span>
                          )}
                        </div>
                      </div>
                    </FormItem>

                    <FormField
                      control={form.control}
                      name="fromBranchId"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>From Branch</FormLabel>
                          <div className="relative">
                            {/* Use a static display instead of a Select component for disabled fields */}
                            <FormControl>
                              <div className="flex h-10 w-full items-center justify-between rounded-md border border-input bg-muted px-3 py-2 text-sm">
                                {field.value && filteredBranches.find(b => b.branch_id.toString() === field.value) ? (
                                  <span>
                                    {filteredBranches.find(b => b.branch_id.toString() === field.value)?.name}
                                    {filteredBranches.find(b => b.branch_id.toString() === field.value)?.code ?
                                      ` (${filteredBranches.find(b => b.branch_id.toString() === field.value)?.code})` : ''}
                                  </span>
                                ) : (
                                  <span className="text-muted-foreground">Your Branch</span>
                                )}
                              </div>
                            </FormControl>
                          </div>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  {/* To City and Branch Selection */}
                  <div className="space-y-4">
                    <FormItem>
                      <FormLabel>To City</FormLabel>
                      <Select
                        onValueChange={(value) => {
                          // Clear any existing value to avoid duplicate keys
                          setSelectedToCity("");
                          // Set after a small delay to ensure React has processed the clearing
                          setTimeout(() => setSelectedToCity(value), 10);
                        }}
                        value={selectedToCity}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select city" />
                        </SelectTrigger>
                        <SelectContent>
                          {cities.map((city) => {
                            // Ensure consistent string conversion for keys
                            const cityIdStr = city.city_id.toString();
                            // Add a prefix to ensure uniqueness between from and to cities
                            return (
                              <SelectItem key={`to-${cityIdStr}`} value={cityIdStr}>
                                {city.name}, {city.state}
                              </SelectItem>
                            );
                          })}
                        </SelectContent>
                      </Select>
                    </FormItem>

                    <FormField
                      control={form.control}
                      name="toBranchId"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>To Branch</FormLabel>
                          <Select
                            onValueChange={(value) => {
                              // Clear any existing value to avoid duplicate keys
                              field.onChange("");
                              // Set after a small delay to ensure React has processed the clearing
                              setTimeout(() => field.onChange(value), 10);
                            }}
                            value={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select branch" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {filteredToBranches
                                .filter(branch => {
                                  // Exclude the current branch (from branch) from the To Branch dropdown
                                  const fromBranchId = form.getValues("fromBranchId");
                                  return branch.branch_id.toString() !== fromBranchId;
                                })
                                .map((branch) => {
                                  // Ensure consistent string conversion for keys
                                  const branchIdStr = branch.branch_id.toString();
                                  // Add a prefix to ensure uniqueness between from and to branches
                                  return (
                                    <SelectItem key={`to-branch-${branchIdStr}`} value={branchIdStr}>
                                      {branch.name} ({branch.code})
                                    </SelectItem>
                                  );
                                })}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <FormLabel>Via Points (Optional)</FormLabel>
                  <div className="border p-4 rounded-md">
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                      {branches.map((branch) => {
                        const fromBranchId = form.getValues("fromBranchId");
                        const toBranchId = form.getValues("toBranchId");

                        // Ensure consistent string conversion for branch_id
                        const branchIdStr = branch.branch_id.toString();

                        // Skip from and to branches in via points
                        if (
                          branchIdStr === fromBranchId ||
                          branchIdStr === toBranchId
                        ) {
                          return null;
                        }

                        const isSelected = selectedViaPoints.includes(branchIdStr);

                        return (
                          <div
                            key={branchIdStr}
                            className={`p-2 border rounded-md cursor-pointer ${
                              isSelected ? 'bg-primary text-primary-foreground' : 'bg-background'
                            }`}
                            onClick={() => handleViaPointChange(branchIdStr)}
                          >
                            {branch.name} ({branch.code})
                          </div>
                        );
                      })}
                    </div>

                    {selectedViaPoints.length > 0 && (
                      <div className="mt-4">
                        <p className="text-sm font-medium">Selected Via Points:</p>
                        <div className="flex flex-wrap gap-2 mt-2">
                          {selectedViaPoints.map(id => {
                            // id is already a string from selectedViaPoints
                            const branch = branches.find(b => b.branch_id.toString() === id);
                            return branch ? (
                              <Badge key={`selected-${id}`} variant="secondary">
                                {branch.name} ({branch.code})
                              </Badge>
                            ) : null;
                          })}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
              <CardFooter className="flex flex-col gap-4">
                <Button
                  type="submit"
                  className="w-full"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Creating Memo...
                    </>
                  ) : (
                    "Create Memo"
                  )}
                </Button>

                <div className="flex items-center justify-center w-full">
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => setDebugMode(!debugMode)}
                  >
                    {debugMode ? "Hide Debug Info" : "Show Debug Info"}
                  </Button>
                </div>

                {debugMode && (
                  <div className="w-full mt-4 p-4 border rounded-md bg-muted/50 text-xs overflow-auto max-h-[300px]">
                    <h4 className="font-bold mb-2">Debug Information:</h4>
                    <div className="space-y-2">
                      <div>
                        <p className="font-semibold">Vehicle Validation:</p>
                        <pre className="whitespace-pre-wrap">
                          {JSON.stringify(vehicleValidation, null, 2)}
                        </pre>
                      </div>
                      <div>
                        <p className="font-semibold">Driver Validations:</p>
                        <pre className="whitespace-pre-wrap">
                          {JSON.stringify(driverValidations, null, 2)}
                        </pre>
                      </div>
                    </div>
                  </div>
                )}
              </CardFooter>
            </Card>
          </form>
        </Form>
      )}

      {/* Available Drivers Dialog */}
      <Dialog open={showDriversDialog} onOpenChange={setShowDriversDialog}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>Available Drivers</DialogTitle>
            <DialogDescription>
              These are the active drivers registered in the system. You can use either their Driver ID or Mobile Number for validation.
            </DialogDescription>
          </DialogHeader>
          <div className="max-h-[400px] overflow-y-auto">
            <table className="w-full border-collapse">
              <thead>
                <tr className="bg-muted">
                  <th className="p-2 text-left">Name</th>
                  <th className="p-2 text-left">Driver ID</th>
                  <th className="p-2 text-left">Mobile Number</th>
                  <th className="p-2 text-left">Action</th>
                </tr>
              </thead>
              <tbody>
                {availableDrivers.length === 0 ? (
                  <tr>
                    <td colSpan={4} className="p-4 text-center text-muted-foreground">
                      No active drivers found
                    </td>
                  </tr>
                ) : (
                  availableDrivers.map((driver) => (
                    <tr key={driver.driver_id} className="border-b">
                      <td className="p-2">{driver.name}</td>
                      <td className="p-2">{driver.driver_number || 'N/A'}</td>
                      <td className="p-2">{driver.phone_number}</td>
                      <td className="p-2">
                        <div className="flex gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              // Copy the driver ID to clipboard
                              if (driver.driver_number) {
                                navigator.clipboard.writeText(driver.driver_number);
                                toast({
                                  title: "Copied ID",
                                  description: `Driver ID ${driver.driver_number} copied to clipboard`,
                                });
                              }
                            }}
                            disabled={!driver.driver_number}
                          >
                            <Copy className="h-4 w-4 mr-1" />
                            Copy ID
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              // Copy the mobile number to clipboard
                              navigator.clipboard.writeText(driver.phone_number);
                              toast({
                                title: "Copied Number",
                                description: `Mobile number ${driver.phone_number} copied to clipboard`,
                              });
                            }}
                          >
                            <Copy className="h-4 w-4 mr-1" />
                            Copy Number
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
          <DialogFooter>
            <Button onClick={() => setShowDriversDialog(false)}>Close</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Success Dialog */}
      <Dialog open={showSuccessDialog} onOpenChange={handleDialogClose}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Memo Created Successfully</DialogTitle>
            <DialogDescription>
              A new memo has been created with the following details:
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="font-medium">Memo Number:</span>
                <span className="font-mono">{generatedMemoNumber}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="font-medium">Vehicle Number:</span>
                <span>{form.getValues("vehicleNumber")}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="font-medium">Drivers:</span>
                <span>{form.getValues("drivers").map(d => d.driverNumber).join(", ")}</span>
              </div>

              {/* Display from and to branches */}
              {branches.length > 0 && (
                <>
                  <div className="flex justify-between items-center">
                    <span className="font-medium">From Branch:</span>
                    <span>
                      {(() => {
                        const fromId = form.getValues("fromBranchId");
                        const branch = branches.find(b => b.branch_id.toString() === fromId);
                        return branch ? `${branch.name} (${branch.code})` : "";
                      })()}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="font-medium">To Branch:</span>
                    <span>
                      {(() => {
                        const toId = form.getValues("toBranchId");
                        const branch = branches.find(b => b.branch_id.toString() === toId);
                        return branch ? `${branch.name} (${branch.code})` : "";
                      })()}
                    </span>
                  </div>
                </>
              )}

              {/* Display via points if any */}
              {selectedViaPoints.length > 0 && (
                <div>
                  <span className="font-medium">Via Points:</span>
                  <div className="flex flex-wrap gap-1 mt-1">
                    {selectedViaPoints.map(id => {
                      const branch = branches.find(b => b.branch_id.toString() === id);
                      return branch ? (
                        <Badge key={`dialog-${id}`} variant="secondary">
                          {branch.name} ({branch.code})
                        </Badge>
                      ) : null;
                    })}
                  </div>
                </div>
              )}
            </div>

            <div className="mt-4 border-t pt-4">
              <p className="text-sm text-muted-foreground">
                This memo has been created and is ready to be loaded. The memo details will be sent to the driver and all branch offices via WhatsApp.
              </p>
            </div>
          </div>
          <DialogFooter>
            <Button onClick={handleDialogClose}>Close</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}


