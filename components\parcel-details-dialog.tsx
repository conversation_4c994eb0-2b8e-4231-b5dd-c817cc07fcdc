"use client"

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/dialog"
import { <PERSON>roll<PERSON>rea } from "@/components/ui/scroll-area"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Loader2 } from "lucide-react"
import { useState, useEffect } from "react"
import { Button } from "@/components/ui/button"
import { Printer } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { format } from "date-fns"

interface ParcelDetailsDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  parcel: {
    parcel_id?: number // Added parcel_id for API calls
    lrn: string
    senderName: string
    senderAddress?: string
    senderPhone?: string
    senderBranch?: string // Origin branch name
    recipientName: string
    recipientAddress?: string
    recipientPhone?: string
    recipientBranch?: string // Destination branch name
    status: string
    bookingDate: string
    weight: string
    dimensions?: string
    price?: string
    paymentMode: string
    instructions?: string
    numberOfItems?: number // Number of items in the parcel
    items: Array<{
      type: string
      description: string
      quantity: number
      weight: string
      amount: number
    }>
    statusHistory: Array<{
      id?: number
      parcel_id?: number
      status: string
      location: string
      timestamp?: string
      date?: string
      branch_id?: number
      vehicle_id?: number
      updated_by?: string
      vehicleNo?: string
      luggageNo?: string
      remarks?: string
    }>
  }
}

const statusColors = {
  "Booked": "bg-blue-100 text-blue-800",
  "In Transit": "bg-purple-100 text-purple-800",
  "To Be Delivered": "bg-green-100 text-green-800",
  "Delivered": "bg-gray-100 text-gray-800",
  "Cancelled": "bg-red-100 text-red-800"
}

export function ParcelDetailsDialog({ open, onOpenChange, parcel }: ParcelDetailsDialogProps) {
  const { toast } = useToast()
  const [activeTab, setActiveTab] = useState("details")
  const [timelineLoading, setTimelineLoading] = useState(false)
  const [statusHistory, setStatusHistory] = useState<any[]>([])
  const [timelineView, setTimelineView] = useState<"timeline" | "detailed">("timeline")
  const [timelineActions, setTimelineActions] = useState<any[]>([])
  const [detailedActions, setDetailedActions] = useState<any[]>([])

  // Reset to details tab when dialog opens with a new parcel
  useEffect(() => {
    if (open) {
      setActiveTab("details");
      setStatusHistory([]);
      setTimelineActions([]);
      setDetailedActions([]);
    }
  }, [open, parcel?.lrn]);

  // Fetch status history when the timeline tab is selected
  useEffect(() => {
    if (activeTab === "timeline" && parcel && timelineActions.length === 0) {
      fetchParcelActions();
    }
  }, [activeTab, parcel]);

  const fetchParcelActions = async () => {
    if (!parcel) return;

    setTimelineLoading(true);
    try {
      if (parcel.parcel_id) {
        // Fetch parcel actions from the new API endpoint
        const response = await fetch(`/api/parcels/${parcel.parcel_id}/actions`);
        if (!response.ok) {
          throw new Error('Failed to fetch parcel actions');
        }

        const data = await response.json();

        console.log("Parcel actions data:", data);

        setTimelineActions(data.timeline || []);
        setDetailedActions(data.detailed || []);

        // Also set the old status history for backward compatibility
        setStatusHistory(data.timeline || []);
        return;
      }

      // Fallback to old status history method
      await fetchStatusHistory();
    } catch (error) {
      console.error('Error fetching parcel actions:', error);
      // Fallback to old method
      await fetchStatusHistory();
    } finally {
      setTimelineLoading(false);
    }
  };

  const fetchStatusHistory = async () => {
    if (!parcel) return;

    try {
      // Use the parcel_id if available, otherwise create a default entry
      if (parcel.parcel_id) {
        // Fetch status history from API using the parcel_id
        const response = await fetch(`/api/parcels/${parcel.parcel_id}`);
        if (!response.ok) {
          throw new Error('Failed to fetch status history');
        }

        const data = await response.json();

        if (data.status_history && data.status_history.length > 0) {
          // Process the status history data
          const processedHistory = data.status_history.map((entry: any) => ({
            ...entry,
            date: entry.timestamp || entry.date || new Date().toISOString()
          }));

          setStatusHistory(processedHistory);
          setTimelineActions(processedHistory);
          return;
        }
      }

      // If we get here, either there's no parcel_id, the API call failed,
      // or there was no status history in the response
      // Create a default entry
      console.log("Using default status history for parcel:", parcel.lrn);
      const defaultHistory = [{
        status: parcel.status || "Booked",
        date: parcel.bookingDate || new Date().toISOString(),
        location: parcel.senderAddress || "Initial booking",
        remarks: "Parcel booked"
      }];

      setStatusHistory(defaultHistory);
      setTimelineActions(defaultHistory);
    } catch (error) {
      console.error('Error fetching status history:', error);
      // Set a default entry on error
      const defaultHistory = [{
        status: parcel.status || "Booked",
        date: parcel.bookingDate || new Date().toISOString(),
        location: parcel.senderAddress || "Initial booking",
        remarks: "Parcel booked"
      }];

      setStatusHistory(defaultHistory);
      setTimelineActions(defaultHistory);
    }
  };

  const handlePrint = () => {
    // Store the current body overflow style
    const originalOverflow = document.body.style.overflow;

    // Hide scrollbars during printing
    document.body.style.overflow = 'hidden';

    // Add print-mode class to body
    document.body.classList.add('print-mode');

    // Reset scroll position of the print container
    const printContainer = document.querySelector('.print-container');
    if (printContainer) {
      const scrollArea = printContainer.closest('[role="region"]');
      if (scrollArea) {
        scrollArea.scrollTop = 0;
      }
    }

    // Trigger print
    window.print();

    // Cleanup after printing
    document.body.style.overflow = originalOverflow;
    document.body.classList.remove('print-mode');
  };

  // The parcel.price already contains the final total amount
  // No need to calculate additional charges as they're already included
  const finalPrice = parseFloat(parcel.price)

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl h-[90vh] flex flex-col p-0 print:h-auto print:max-h-none">
        <div className="px-6 py-4 border-b">
          <DialogHeader className="flex flex-row items-center justify-between">
            <div className="flex flex-col gap-1.5">
              <DialogTitle className="text-xl">
                Parcel Details - {parcel.lrn}
              </DialogTitle>
              <div className="flex items-center gap-4 text-sm text-muted-foreground">
                <span>Booked on {new Date(parcel.bookingDate).toLocaleDateString()}</span>
                <span>•</span>
                <span>{parcel.numberOfItems || parcel.items[0]?.quantity || 'N/A'} items</span>
                <span>•</span>
                <span>{parcel.weight} kg</span>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Badge className={statusColors[parcel.status as keyof typeof statusColors] || "bg-gray-100 text-gray-800"}>
                {parcel.status}
              </Badge>
              <Button
                onClick={handlePrint}
                variant="outline"
                size="icon"
                className="no-print"
              >
                <Printer className="h-4 w-4" />
              </Button>
            </div>
          </DialogHeader>
        </div>

        <ScrollArea className="flex-1 print:overflow-visible print:h-auto">
          <div className="print-container p-6">
            {/* Direct content display without tabs for now */}
            {activeTab === "details" ? (
              <div className="space-y-8">
                {/* Parcel Details Section */}
                <div className="grid grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <h3 className="font-semibold text-lg">Sender Details</h3>
                    <div className="space-y-1">
                      <p className="font-medium">{parcel.senderName}</p>
                      <p className="text-sm text-muted-foreground">{parcel.senderPhone}</p>
                      {parcel.senderBranch && (
                        <div className="mt-2">
                          <p className="text-xs text-muted-foreground">Origin Branch</p>
                          <p className="text-sm font-medium text-blue-600">{parcel.senderBranch}</p>
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="space-y-2">
                    <h3 className="font-semibold text-lg">Recipient Details</h3>
                    <div className="space-y-1">
                      <p className="font-medium">{parcel.recipientName}</p>
                      <p className="text-sm text-muted-foreground">{parcel.recipientPhone}</p>
                      {parcel.recipientBranch && (
                        <div className="mt-2">
                          <p className="text-xs text-muted-foreground">Destination Branch</p>
                          <p className="text-sm font-medium text-green-600">{parcel.recipientBranch}</p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Parcel Specifications */}
                <div className="space-y-4">
                  <h3 className="font-semibold text-lg">Parcel Specifications</h3>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-muted-foreground">Number of Items</p>
                      <p className="font-medium">{parcel.numberOfItems || parcel.items[0]?.quantity || 'N/A'}</p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Weight</p>
                      <p className="font-medium">{parcel.weight} kg</p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Payment Mode</p>
                      <p className="font-medium">{parcel.paymentMode}</p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Current Status</p>
                      <Badge className={statusColors[parcel.status as keyof typeof statusColors] || "bg-gray-100 text-gray-800"}>
                        {parcel.status}
                      </Badge>
                    </div>
                    {parcel.instructions && (
                      <div>
                        <p className="text-sm text-muted-foreground">Special Instructions</p>
                        <p className="font-medium">{parcel.instructions}</p>
                      </div>
                    )}
                  </div>
                </div>

                {/* Price Information */}
                <div className="space-y-4">
                  <h3 className="font-semibold text-lg">Price Information</h3>
                  <div className="space-y-2">
                    <div className="flex justify-between pt-2 border-t font-semibold">
                      <span>Total Amount</span>
                      <span>₹{finalPrice}</span>
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="font-semibold text-lg">Parcel History</h3>

                  {/* Toggle between Timeline and Detailed views */}
                  <div className="flex items-center gap-2">
                    <Button
                      variant={timelineView === "timeline" ? "default" : "outline"}
                      size="sm"
                      onClick={() => setTimelineView("timeline")}
                    >
                      Timeline View
                    </Button>
                    <Button
                      variant={timelineView === "detailed" ? "default" : "outline"}
                      size="sm"
                      onClick={() => setTimelineView("detailed")}
                    >
                      Detailed Actions
                    </Button>
                  </div>
                </div>

                {timelineLoading ? (
                  <div className="flex flex-col items-center justify-center py-12">
                    <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
                    <p className="text-muted-foreground">Loading parcel history...</p>
                  </div>
                ) : timelineView === "timeline" ? (
                  <div className="relative">
                    {/* Timeline connector */}
                    <div className="absolute left-3 top-5 bottom-5 w-0.5 bg-gray-200"></div>

                    {/* Timeline entries - only status changes */}
                    <div className="space-y-6">
                      {timelineActions.length > 0 ? (
                        timelineActions
                          .sort((a, b) => {
                            const dateA = new Date(a.action_timestamp || a.date || a.timestamp || 0);
                            const dateB = new Date(b.action_timestamp || b.date || b.timestamp || 0);
                            return dateA.getTime() - dateB.getTime();
                          })
                          .map((action, index) => (
                        <div key={index} className="relative pl-10">
                          {/* Timeline dot */}
                          <div className={`absolute left-0 top-1.5 w-6 h-6 rounded-full flex items-center justify-center ${statusColors[(action.action_type || action.status) as keyof typeof statusColors] || "bg-gray-100"}`}>
                            <div className="w-2 h-2 rounded-full bg-current"></div>
                          </div>

                          {/* Timeline content */}
                          <div className="bg-white border rounded-lg p-4 shadow-sm">
                            <div className="flex items-center justify-between mb-2">
                              <h4 className="font-medium text-gray-900">{action.action_type || action.status}</h4>
                              <span className="text-sm text-gray-500">
                                {format(new Date(action.action_timestamp || action.date || action.timestamp), "dd/MM/yyyy, HH:mm:ss")}
                              </span>
                            </div>
                            <p className="text-sm text-gray-600 mb-1">
                              <strong>Location:</strong> {action.branch_name || action.location_name || action.location || "System update"}
                            </p>
                            {action.remarks && (
                              <p className="text-sm text-gray-600">
                                <strong>Remarks:</strong> {action.remarks}
                              </p>
                            )}
                            {action.vehicle_registration && (
                              <p className="text-sm text-gray-600">
                                <strong>Vehicle:</strong> {action.vehicle_registration}
                              </p>
                            )}
                          </div>
                        </div>
                      ))) : (
                        <div className="text-muted-foreground text-sm py-4">No timeline data available</div>
                      )}
                    </div>
                  </div>
                ) : (
                  /* Detailed Actions Table View */
                  <div className="space-y-4">
                    {detailedActions.length > 0 ? (
                      <div className="border rounded-lg overflow-hidden">
                        <div className="overflow-x-auto" style={{ maxWidth: '100%' }}>
                          <table className="w-full text-sm min-w-[700px]">
                            <thead className="bg-gray-50">
                              <tr>
                                <th className="px-4 py-3 text-left font-medium text-gray-900 min-w-[140px]">Date/Time</th>
                                <th className="px-4 py-3 text-left font-medium text-gray-900 min-w-[100px]">Action</th>
                                <th className="px-4 py-3 text-left font-medium text-gray-900 min-w-[140px]">Location</th>
                                <th className="px-4 py-3 text-left font-medium text-gray-900 min-w-[80px]">Quantity</th>
                                <th className="px-4 py-3 text-left font-medium text-gray-900 min-w-[240px]">Details</th>
                              </tr>
                            </thead>
                            <tbody className="divide-y divide-gray-200">
                              {detailedActions
                                .sort((a, b) => {
                                  const dateA = new Date(a.action_timestamp || 0);
                                  const dateB = new Date(b.action_timestamp || 0);
                                  return dateA.getTime() - dateB.getTime();
                                })
                                .map((action, index) => (
                                <tr key={index} className="hover:bg-gray-50">
                                  <td className="px-4 py-3 text-gray-900">
                                    {format(new Date(action.action_timestamp), "dd/MM/yyyy HH:mm")}
                                  </td>
                                  <td className="px-4 py-3">
                                    <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${statusColors[action.action_type as keyof typeof statusColors] || "bg-gray-100 text-gray-800"}`}>
                                      {action.action_type}
                                    </span>
                                  </td>
                                  <td className="px-4 py-3 text-gray-900">
                                    {action.branch_name || action.location_name || "-"}
                                  </td>
                                  <td className="px-4 py-3 text-gray-900">
                                    {action.quantity || action.quantity_loaded || action.quantity_received || "-"}
                                  </td>
                                  <td className="px-4 py-3 text-gray-600">
                                    {action.vehicle_registration && `Vehicle: ${action.vehicle_registration}`}
                                    {action.loading_type && ` (${action.loading_type})`}
                                    {action.destination_branch_name && ` → ${action.destination_branch_name}`}
                                    {action.remarks && ` | ${action.remarks}`}
                                  </td>
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>
                      </div>
                    ) : (
                      <div className="text-muted-foreground text-sm py-4">No detailed actions available</div>
                    )}
                  </div>
                )}
              </div>
            )}
          </div>
        </ScrollArea>

        <div className="px-6 py-2 border-t">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="details">Parcel Details</TabsTrigger>
              <TabsTrigger value="timeline">Status Timeline</TabsTrigger>
            </TabsList>
          </Tabs>
        </div>
      </DialogContent>
    </Dialog>
  )
}
