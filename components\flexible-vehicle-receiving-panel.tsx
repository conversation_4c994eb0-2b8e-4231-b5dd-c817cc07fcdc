"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { useToast } from "@/components/ui/use-toast"
import { Loader2, Search, Plus, Minus, Truck, Package, AlertCircle, CheckCircle, FileText } from "lucide-react"
import { ReceivingReport } from "./receiving-report"

interface Vehicle {
  vehicle_id: number
  registration_number: string
  vehicle_type: string
  make_model: string
  current_status: string
}

interface ParcelToReceive {
  parcel_id: number
  lr_number: string
  sender_name: string
  recipient_name: string
  number_of_items: number
  current_status: string
  sender_branch: { name: string; code: string }
  delivery_branch: { name: string; code: string }
  weight?: number
  total_amount?: number
  payment_mode?: string
  received_quantity: number
}

export function FlexibleVehicleReceivingPanel() {
  const { toast } = useToast()
  const [vehicleNumber, setVehicleNumber] = useState("")
  const [currentLR, setCurrentLR] = useState("")
  const [receivedQuantity, setReceivedQuantity] = useState("1")
  const [isSearching, setIsSearching] = useState(false)
  const [isValidating, setIsValidating] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [validationError, setValidationError] = useState<string | null>(null)
  const [userBranchId, setUserBranchId] = useState<string | null>(null)
  const [vehicle, setVehicle] = useState<Vehicle | null>(null)
  const [parcelsToReceive, setParcelsToReceive] = useState<ParcelToReceive[]>([])
  const [step, setStep] = useState<"search" | "lr" | "confirmation" | "complete" | "report">("search")
  const [receivingResults, setReceivingResults] = useState<any>(null)

  // Get user branch ID from localStorage
  useEffect(() => {
    const branchId = localStorage.getItem('userBranchId')
    setUserBranchId(branchId)
  }, [])

  // Search for vehicle
  const searchVehicle = async () => {
    if (!vehicleNumber.trim()) {
      toast({
        title: "Missing Information",
        description: "Please enter a vehicle number",
        variant: "destructive",
      })
      return
    }

    setIsSearching(true)
    setError(null)

    try {
      const params = new URLSearchParams({
        registration_number: vehicleNumber.trim()
      })

      const response = await fetch(`/api/vehicles/search?${params.toString()}`)
      const data = await response.json()

      if (response.ok && data.available) {
        setVehicle(data.vehicle)
        setStep("lr")
      } else {
        setError(data.error || "Vehicle not found or not available for receiving")
      }
    } catch (error: any) {
      console.error('Error searching vehicle:', error)
      setError('Failed to search vehicle. Please try again.')
    } finally {
      setIsSearching(false)
    }
  }

  // Handle key press in vehicle input
  const handleVehicleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      searchVehicle()
    }
  }

  // Handle key press in LR input
  const handleLRKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      validateAndAddLR()
    }
  }

  // Validate and add LR to receiving list
  const validateAndAddLR = async () => {
    if (!currentLR.trim()) {
      toast({
        title: "Missing Information",
        description: "Please enter an LR number",
        variant: "destructive",
      })
      return
    }

    if (!vehicle || !userBranchId) {
      setValidationError("Vehicle information is missing. Please try again.")
      return
    }

    const quantity = parseInt(receivedQuantity) || 1
    if (quantity <= 0) {
      setValidationError("Received quantity must be greater than 0")
      return
    }

    setIsValidating(true)
    setValidationError(null)

    try {
      const params = new URLSearchParams({
        lr_number: currentLR.trim()
      })

      const response = await fetch(`/api/parcels/validate-lr-simple?${params.toString()}`)
      const data = await response.json()

      if (response.ok && data.valid) {
        // Check if this LR is already in the list
        const existingParcelIndex = parcelsToReceive.findIndex(p => p.lr_number === data.parcel.lr_number)

        if (existingParcelIndex >= 0) {
          // Update quantity for existing parcel
          setParcelsToReceive(prev => prev.map((p, index) => 
            index === existingParcelIndex 
              ? { ...p, received_quantity: p.received_quantity + quantity }
              : p
          ))

          toast({
            title: "Quantity Updated",
            description: `Added ${quantity} items to existing LR ${data.parcel.lr_number}`,
          })
        } else {
          // Add new parcel to the list
          const newParcel: ParcelToReceive = {
            ...data.parcel,
            received_quantity: quantity
          }

          setParcelsToReceive(prev => [...prev, newParcel])

          toast({
            title: "Parcel Added",
            description: `LR ${data.parcel.lr_number} added with ${quantity} items`,
          })
        }

        // Clear inputs
        setCurrentLR("")
        setReceivedQuantity("1")
      } else {
        setValidationError(data.message || "Invalid LR number")
      }
    } catch (error: any) {
      console.error('Error validating LR:', error)
      setValidationError('Failed to validate LR. Please try again.')
    } finally {
      setIsValidating(false)
    }
  }

  // Update quantity for a parcel in the list
  const updateParcelQuantity = (lr_number: string, newQuantity: number) => {
    if (newQuantity <= 0) {
      // Remove parcel if quantity is 0 or less
      setParcelsToReceive(prev => prev.filter(p => p.lr_number !== lr_number))
    } else {
      setParcelsToReceive(prev => prev.map(p => 
        p.lr_number === lr_number 
          ? { ...p, received_quantity: newQuantity }
          : p
      ))
    }
  }

  // Remove parcel from list
  const removeParcel = (lr_number: string) => {
    setParcelsToReceive(prev => prev.filter(p => p.lr_number !== lr_number))
  }

  // Submit receiving operation
  const submitReceiving = async () => {
    if (parcelsToReceive.length === 0) {
      toast({
        title: "No Parcels",
        description: "Please add at least one parcel to receive",
        variant: "destructive",
      })
      return
    }

    if (!vehicle || !userBranchId) {
      setError("Missing vehicle or branch information")
      return
    }

    setIsSubmitting(true)
    setError(null)

    try {
      const requestBody = {
        vehicle_id: vehicle.vehicle_id,
        vehicle_registration: vehicle.registration_number,
        receiving_branch_id: parseInt(userBranchId),
        parcels: parcelsToReceive.map(p => ({
          lr_number: p.lr_number,
          received_quantity: p.received_quantity
        })),
        remarks: `Received via flexible receiving system`
      }

      const response = await fetch('/api/parcels/receive-flexible', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestBody)
      })

      const data = await response.json()

      if (response.ok) {
        setReceivingResults(data)
        toast({
          title: "Receiving Completed",
          description: `Successfully processed ${data.summary.successful} out of ${data.summary.total_parcels} parcels`,
        })
        setStep("complete")
      } else {
        setError(data.error || "Failed to complete receiving operation")
      }
    } catch (error: any) {
      console.error('Error submitting receiving:', error)
      setError('Failed to submit receiving operation. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  // Reset to start over
  const resetForm = () => {
    setVehicleNumber("")
    setCurrentLR("")
    setReceivedQuantity("1")
    setVehicle(null)
    setParcelsToReceive([])
    setError(null)
    setValidationError(null)
    setStep("search")
  }

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Truck className="h-5 w-5" />
          Flexible Vehicle Receiving
        </CardTitle>
        <CardDescription>
          Receive parcels from any vehicle without loading chart requirements
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {step === "search" && (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="vehicle-number">Vehicle Registration Number</Label>
              <div className="flex space-x-2">
                <Input
                  id="vehicle-number"
                  placeholder="Enter vehicle registration number"
                  value={vehicleNumber}
                  onChange={(e) => setVehicleNumber(e.target.value)}
                  onKeyDown={handleVehicleKeyPress}
                  disabled={isSearching}
                />
                <Button onClick={searchVehicle} disabled={isSearching || !vehicleNumber.trim()}>
                  {isSearching ? <Loader2 className="h-4 w-4 animate-spin" /> : <Search className="h-4 w-4" />}
                  Search
                </Button>
              </div>
            </div>
          </div>
        )}

        {step === "lr" && vehicle && (
          <div className="space-y-6">
            <div className="bg-green-50 p-4 rounded-lg border border-green-200">
              <div className="flex items-center gap-2 text-green-800 font-medium mb-2">
                <CheckCircle className="h-4 w-4" />
                Vehicle Found
              </div>
              <div className="text-sm text-green-700">
                <p><strong>Registration:</strong> {vehicle.registration_number}</p>
                <p><strong>Type:</strong> {vehicle.vehicle_type}</p>
                <p><strong>Model:</strong> {vehicle.make_model}</p>
                <p><strong>Status:</strong> <Badge variant="outline">{vehicle.current_status}</Badge></p>
              </div>
            </div>

            {validationError && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Error</AlertTitle>
                <AlertDescription>{validationError}</AlertDescription>
              </Alert>
            )}

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="lr-number">LR Number</Label>
                <Input
                  id="lr-number"
                  placeholder="Enter LR number"
                  value={currentLR}
                  onChange={(e) => setCurrentLR(e.target.value)}
                  onKeyDown={handleLRKeyPress}
                  disabled={isValidating}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="quantity">Received Quantity</Label>
                <Input
                  id="quantity"
                  type="number"
                  min="1"
                  placeholder="1"
                  value={receivedQuantity}
                  onChange={(e) => setReceivedQuantity(e.target.value)}
                  disabled={isValidating}
                />
              </div>
              <div className="space-y-2">
                <Label>&nbsp;</Label>
                <Button 
                  onClick={validateAndAddLR} 
                  disabled={isValidating || !currentLR.trim()}
                  className="w-full"
                >
                  {isValidating ? <Loader2 className="h-4 w-4 animate-spin" /> : <Plus className="h-4 w-4" />}
                  Add Parcel
                </Button>
              </div>
            </div>

            {parcelsToReceive.length > 0 && (
              <div className="space-y-4">
                <Separator />
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-medium">Parcels to Receive ({parcelsToReceive.length})</h3>
                  <Button onClick={() => setStep("confirmation")} disabled={parcelsToReceive.length === 0}>
                    <Package className="h-4 w-4 mr-2" />
                    Proceed to Receive
                  </Button>
                </div>
                
                <div className="space-y-2">
                  {parcelsToReceive.map((parcel) => (
                    <div key={parcel.lr_number} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex-1">
                        <div className="font-medium">{parcel.lr_number}</div>
                        <div className="text-sm text-gray-600">
                          {parcel.sender_name} → {parcel.recipient_name}
                        </div>
                        <div className="text-xs text-gray-500">
                          {parcel.sender_branch.name} → {parcel.delivery_branch.name} | 
                          Status: <Badge variant="outline" className="ml-1">{parcel.current_status}</Badge>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => updateParcelQuantity(parcel.lr_number, parcel.received_quantity - 1)}
                        >
                          <Minus className="h-3 w-3" />
                        </Button>
                        <span className="w-8 text-center">{parcel.received_quantity}</span>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => updateParcelQuantity(parcel.lr_number, parcel.received_quantity + 1)}
                        >
                          <Plus className="h-3 w-3" />
                        </Button>
                        <Button
                          size="sm"
                          variant="destructive"
                          onClick={() => removeParcel(parcel.lr_number)}
                        >
                          Remove
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}

        {step === "confirmation" && (
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Confirm Receiving Operation</h3>
            <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
              <p className="text-blue-800">
                You are about to receive <strong>{parcelsToReceive.length} parcels</strong> with a total of{" "}
                <strong>{parcelsToReceive.reduce((sum, p) => sum + p.received_quantity, 0)} items</strong> from vehicle{" "}
                <strong>{vehicle?.registration_number}</strong>.
              </p>
            </div>
            
            <div className="flex gap-2">
              <Button onClick={() => setStep("lr")} variant="outline">
                Back to Add Parcels
              </Button>
              <Button onClick={submitReceiving} disabled={isSubmitting}>
                {isSubmitting ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : null}
                Confirm Receiving
              </Button>
            </div>
          </div>
        )}

        {step === "complete" && (
          <div className="space-y-4 text-center">
            <div className="bg-green-50 p-6 rounded-lg border border-green-200">
              <CheckCircle className="h-12 w-12 text-green-600 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-green-800 mb-2">Receiving Completed Successfully!</h3>
              <p className="text-green-700">
                All parcels have been processed and recorded in the system.
              </p>
            </div>

            <div className="flex gap-2 justify-center">
              <Button onClick={() => setStep("report")} variant="outline">
                <FileText className="h-4 w-4 mr-2" />
                View Report
              </Button>
              <Button onClick={resetForm}>
                Start New Receiving Operation
              </Button>
            </div>
          </div>
        )}

        {step === "report" && receivingResults && vehicle && userBranchId && (
          <ReceivingReport
            reportData={{
              vehicle: {
                registration_number: vehicle.registration_number,
                vehicle_type: vehicle.vehicle_type,
                make_model: vehicle.make_model
              },
              receiving_branch: {
                name: "Current Branch", // TODO: Get actual branch name
                code: userBranchId
              },
              receiving_date: new Date().toISOString(),
              operator_name: "Current User", // TODO: Get actual user name
              parcels: receivingResults.results.map((result: any) => ({
                lr_number: result.lr_number,
                sender_name: result.parcel_details?.sender_name || "N/A",
                recipient_name: result.parcel_details?.recipient_name || "N/A",
                sender_branch: result.parcel_details?.sender_branch || "N/A",
                delivery_branch: result.parcel_details?.delivery_branch || "N/A",
                original_quantity: result.original_quantity || 0,
                received_quantity: result.received_quantity || 0,
                status: result.success ? "success" : "failed",
                warning: result.warning
              })),
              summary: receivingResults.summary
            }}
            onClose={() => setStep("complete")}
          />
        )}
      </CardContent>
    </Card>
  )
}
