"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import { useToast } from "@/hooks/use-toast"
import { Loader2, Search, Plus, Minus, Truck, Package, AlertCircle, CheckCircle, Trash2 } from "lucide-react"
import { ReceivingCompletionDialog } from "./receiving-completion-dialog"

interface Vehicle {
  vehicle_id: number
  registration_number: string
  vehicle_type: string
  make_model: string
  current_status: string
}

interface LREntry {
  lrNumber: string
  quantity: number
  isValid: boolean
  validationMessage?: string
  parcelDetails?: {
    sender_name: string
    recipient_name: string
    sender_branch: string
    delivery_branch: string
    current_status: string
  }
}

export function FlexibleVehicleReceivingPanel() {
  const { toast } = useToast()

  // Step management
  const [step, setStep] = useState<"vehicle" | "lr" | "confirmation" | "complete">("vehicle")
  const [showCompletionDialog, setShowCompletionDialog] = useState(false)
  const [completionData, setCompletionData] = useState<any>(null)

  // Vehicle selection
  const [vehicleNumber, setVehicleNumber] = useState("")
  const [isSearchingVehicle, setIsSearchingVehicle] = useState(false)
  const [selectedVehicle, setSelectedVehicle] = useState<Vehicle | null>(null)

  // LR entry
  const [currentLR, setCurrentLR] = useState("")
  const [currentQuantity, setCurrentQuantity] = useState("1")
  const [lrEntries, setLrEntries] = useState<LREntry[]>([])
  const [isValidating, setIsValidating] = useState(false)

  // General state
  const [error, setError] = useState<string | null>(null)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [userBranchId, setUserBranchId] = useState<string | null>(null)

  // Get user branch ID from localStorage
  useEffect(() => {
    console.log("🏢 useEffect: Getting user branch ID from localStorage")
    const branchId = localStorage.getItem('userBranchId')
    console.log("  - branchId from localStorage:", branchId)
    console.log("  - branchId type:", typeof branchId)
    setUserBranchId(branchId)
    console.log("  - userBranchId state should now be set to:", branchId)
  }, [])

  // Helper function to get current branch ID (with fallback)
  const getCurrentBranchId = () => {
    const currentBranchId = userBranchId || localStorage.getItem('userBranchId')
    console.log("🏢 getCurrentBranchId called:")
    console.log("  - userBranchId state:", userBranchId)
    console.log("  - localStorage value:", localStorage.getItem('userBranchId'))
    console.log("  - returning:", currentBranchId)
    return currentBranchId
  }

  // Search for vehicle
  const searchVehicle = async () => {
    console.log("🚗 searchVehicle called")
    console.log("  - vehicleNumber:", vehicleNumber)
    console.log("  - vehicleNumber.trim():", vehicleNumber.trim())

    if (!vehicleNumber.trim()) {
      console.log("❌ Vehicle search failed: No vehicle number provided")
      toast({
        title: "Missing Information",
        description: "Please enter a vehicle number",
        variant: "destructive",
      })
      return
    }

    setIsSearchingVehicle(true)
    setError(null)

    try {
      const params = new URLSearchParams({
        registration_number: vehicleNumber.trim()
      })

      console.log("🌐 Making vehicle search API call")
      console.log("  - URL:", `/api/vehicles/search?${params.toString()}`)

      const response = await fetch(`/api/vehicles/search?${params.toString()}`)
      const data = await response.json()

      console.log("📥 Vehicle search response:")
      console.log("  - response.ok:", response.ok)
      console.log("  - response.status:", response.status)
      console.log("  - data:", data)
      console.log("  - data.available:", data.available)
      console.log("  - data.vehicle:", data.vehicle)

      if (response.ok && data.available) {
        console.log("✅ Vehicle found, setting selectedVehicle")
        console.log("  - Vehicle details:", data.vehicle)

        setSelectedVehicle(data.vehicle)
        setStep("lr")

        console.log("  - selectedVehicle state should now be set")
        console.log("  - Moving to 'lr' step")

        toast({
          title: "Vehicle Found",
          description: `Vehicle ${data.vehicle.registration_number} is ready for receiving`,
        })
      } else {
        console.log("❌ Vehicle search failed:", data.error)
        setError(data.error || "Vehicle not found or not available for receiving")
      }
    } catch (error: any) {
      console.error('❌ Error searching vehicle:', error)
      console.log("Error details:", {
        message: error.message,
        stack: error.stack,
        name: error.name
      })
      setError('Failed to search vehicle. Please try again.')
    } finally {
      setIsSearchingVehicle(false)
      console.log("🏁 searchVehicle function completed")
    }
  }

  // Handle key press in vehicle input
  const handleVehicleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      searchVehicle()
    }
  }

  // Handle key press in LR input
  const handleLRKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      addLREntry()
    }
  }

  // Validate LR number
  const validateLR = async (lrNumber: string): Promise<LREntry> => {
    setIsValidating(true)

    try {
      const params = new URLSearchParams({
        lr_number: lrNumber
      })

      const response = await fetch(`/api/parcels/validate-lr-simple?${params.toString()}`)
      const data = await response.json()

      if (response.ok && data.valid) {
        return {
          lrNumber,
          quantity: parseInt(currentQuantity) || 1,
          isValid: true,
          parcelDetails: {
            sender_name: data.parcel.sender_name,
            recipient_name: data.parcel.recipient_name,
            sender_branch: data.parcel.sender_branch?.name || 'Unknown',
            delivery_branch: data.parcel.delivery_branch?.name || 'Unknown',
            current_status: data.parcel.current_status
          }
        }
      } else {
        return {
          lrNumber,
          quantity: parseInt(currentQuantity) || 1,
          isValid: false,
          validationMessage: data.message || 'Invalid LR number'
        }
      }
    } catch (error: any) {
      console.error('Error validating LR:', error)
      return {
        lrNumber,
        quantity: parseInt(currentQuantity) || 1,
        isValid: false,
        validationMessage: 'Failed to validate LR number'
      }
    } finally {
      setIsValidating(false)
    }
  }

  // Add LR entry
  const addLREntry = async () => {
    if (!currentLR.trim()) {
      toast({
        title: "Missing Information",
        description: "Please enter an LR number",
        variant: "destructive",
      })
      return
    }

    if (!selectedVehicle) {
      toast({
        title: "Vehicle Required",
        description: "Please select a vehicle first",
        variant: "destructive",
      })
      return
    }

    const quantity = parseInt(currentQuantity) || 1
    if (quantity <= 0) {
      toast({
        title: "Invalid Quantity",
        description: "Quantity must be greater than 0",
        variant: "destructive",
      })
      return
    }

    // Check if LR already exists
    const existingIndex = lrEntries.findIndex(entry => entry.lrNumber === currentLR.trim())

    if (existingIndex >= 0) {
      // Update existing entry quantity
      setLrEntries(prev => prev.map((entry, index) =>
        index === existingIndex
          ? { ...entry, quantity: entry.quantity + quantity }
          : entry
      ))

      toast({
        title: "Quantity Updated",
        description: `Added ${quantity} items to existing LR ${currentLR.trim()}`,
      })
    } else {
      // Validate and add new entry
      const validatedEntry = await validateLR(currentLR.trim())
      setLrEntries(prev => [...prev, validatedEntry])

      if (validatedEntry.isValid) {
        toast({
          title: "LR Added",
          description: `LR ${currentLR.trim()} added successfully`,
        })
      } else {
        toast({
          title: "Validation Failed",
          description: validatedEntry.validationMessage || "LR validation failed",
          variant: "destructive",
        })
      }
    }

    // Clear inputs
    setCurrentLR("")
    setCurrentQuantity("1")
  }

  // Adjust quantity for an LR entry
  const adjustQuantity = (index: number, change: number) => {
    setLrEntries(prev => prev.map((entry, i) =>
      i === index
        ? { ...entry, quantity: Math.max(1, entry.quantity + change) }
        : entry
    ))
  }

  // Remove LR entry
  const removeLREntry = (index: number) => {
    setLrEntries(prev => prev.filter((_, i) => i !== index))
  }

  // Submit receiving operation
  const submitReceiving = async () => {
    console.log("🚀 submitReceiving called - Starting debug logging")
    console.log("📊 Current state values:")
    console.log("  - lrEntries:", lrEntries)
    console.log("  - lrEntries.length:", lrEntries.length)
    console.log("  - selectedVehicle:", selectedVehicle)
    console.log("  - userBranchId:", userBranchId)
    console.log("  - userBranchId type:", typeof userBranchId)
    console.log("  - step:", step)

    // Check localStorage directly
    const branchIdFromStorage = localStorage.getItem('userBranchId')
    console.log("  - branchIdFromStorage:", branchIdFromStorage)
    console.log("  - branchIdFromStorage type:", typeof branchIdFromStorage)

    if (lrEntries.length === 0) {
      console.log("❌ Validation failed: No LR entries")
      toast({
        title: "No Parcels",
        description: "Please add at least one parcel to receive",
        variant: "destructive",
      })
      return
    }

    console.log("✅ LR entries validation passed")

    // Enhanced validation logging
    console.log("🔍 Detailed validation check:")
    console.log("  - selectedVehicle exists:", !!selectedVehicle)
    console.log("  - selectedVehicle details:", selectedVehicle ? {
      vehicle_id: selectedVehicle.vehicle_id,
      registration_number: selectedVehicle.registration_number,
      vehicle_type: selectedVehicle.vehicle_type,
      current_status: selectedVehicle.current_status
    } : "NULL")
    console.log("  - userBranchId exists:", !!userBranchId)
    console.log("  - userBranchId value:", userBranchId)
    console.log("  - userBranchId is truthy:", !!userBranchId)
    console.log("  - userBranchId is not empty string:", userBranchId !== "")
    console.log("  - userBranchId is not null:", userBranchId !== null)
    console.log("  - userBranchId is not undefined:", userBranchId !== undefined)

    // Use helper function to get current branch ID with fallback
    const currentBranchId = getCurrentBranchId()

    if (!selectedVehicle || !currentBranchId) {
      console.log("❌ Validation failed: Missing vehicle or branch information")
      console.log("  - selectedVehicle missing:", !selectedVehicle)
      console.log("  - currentBranchId missing:", !currentBranchId)
      console.log("  - userBranchId state:", userBranchId)

      // Try to refresh userBranchId from localStorage one more time
      const freshBranchId = localStorage.getItem('userBranchId')
      console.log("  - Attempting to refresh userBranchId from localStorage:", freshBranchId)

      if (freshBranchId && !userBranchId) {
        console.log("  - Setting userBranchId from localStorage and retrying")
        setUserBranchId(freshBranchId)
        toast({
          title: "Branch Information Refreshed",
          description: "Please try again - branch information has been refreshed",
        })
        return
      }

      // Show detailed error message
      let errorMessage = "Missing information: "
      if (!selectedVehicle) errorMessage += "Vehicle not selected. "
      if (!currentBranchId) errorMessage += "Branch ID not found. "

      setError(errorMessage)
      return
    }

    console.log("✅ Vehicle and branch validation passed")

    // Check for invalid entries
    const invalidEntries = lrEntries.filter(entry => !entry.isValid)
    console.log("🔍 Invalid entries check:")
    console.log("  - invalidEntries:", invalidEntries)
    console.log("  - invalidEntries.length:", invalidEntries.length)

    if (invalidEntries.length > 0) {
      console.log("❌ Validation failed: Invalid LR entries")
      toast({
        title: "Invalid Entries",
        description: `Please fix ${invalidEntries.length} invalid LR entries before proceeding`,
        variant: "destructive",
      })
      return
    }

    console.log("✅ All validations passed, proceeding with API call")

    setIsSubmitting(true)
    setError(null)

    try {
      const requestBody = {
        vehicle_id: selectedVehicle.vehicle_id,
        vehicle_registration: selectedVehicle.registration_number,
        receiving_branch_id: parseInt(currentBranchId),
        parcels: lrEntries.map(entry => ({
          lr_number: entry.lrNumber,
          received_quantity: entry.quantity
        })),
        remarks: `Received via flexible receiving system`
      }

      console.log("📤 Request body prepared:")
      console.log("  - vehicle_id:", requestBody.vehicle_id)
      console.log("  - vehicle_registration:", requestBody.vehicle_registration)
      console.log("  - receiving_branch_id:", requestBody.receiving_branch_id)
      console.log("  - parcels count:", requestBody.parcels.length)
      console.log("  - parcels:", requestBody.parcels)
      console.log("  - remarks:", requestBody.remarks)
      console.log("  - Full request body:", JSON.stringify(requestBody, null, 2))

      console.log("🌐 Making API call to /api/parcels/receive-flexible")

      const response = await fetch('/api/parcels/receive-flexible', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestBody)
      })

      console.log("📥 API response received:")
      console.log("  - response.ok:", response.ok)
      console.log("  - response.status:", response.status)
      console.log("  - response.statusText:", response.statusText)

      const data = await response.json()
      console.log("  - response data:", data)

      if (response.ok) {
        console.log("✅ API call successful, preparing completion data")

        // Prepare completion data
        const completionInfo = {
          vehicle: selectedVehicle,
          parcels: lrEntries,
          results: data.results,
          summary: data.summary,
          receiving_date: new Date().toISOString()
        }

        console.log("📋 Completion info prepared:", completionInfo)

        setCompletionData(completionInfo)
        setShowCompletionDialog(true)
        setStep("complete")

        console.log("✅ Receiving operation completed successfully")
      } else {
        console.log("❌ API call failed:", data.error)
        setError(data.error || "Failed to complete receiving operation")
      }
    } catch (error: any) {
      console.error('❌ Error submitting receiving:', error)
      console.log("Error details:", {
        message: error.message,
        stack: error.stack,
        name: error.name
      })
      setError('Failed to submit receiving operation. Please try again.')
    } finally {
      setIsSubmitting(false)
      console.log("🏁 submitReceiving function completed")
    }
  }

  // Reset form
  const resetForm = () => {
    setStep("vehicle")
    setVehicleNumber("")
    setSelectedVehicle(null)
    setLrEntries([])
    setCurrentLR("")
    setCurrentQuantity("1")
    setError(null)
    setShowCompletionDialog(false)
    setCompletionData(null)
  }

  // Handle completion dialog close
  const handleCompletionClose = () => {
    setShowCompletionDialog(false)
    resetForm()
  }

  return (
    <div className="space-y-4">
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Step 1: Vehicle Search */}
      {step === "vehicle" && (
        <Card>
          <CardHeader>
            <CardTitle>Search Vehicle</CardTitle>
            <CardDescription>Enter the vehicle registration number to start receiving</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="vehicle-number">Vehicle Registration Number</Label>
              <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2">
                <Input
                  id="vehicle-number"
                  placeholder="Enter vehicle number (e.g., KA01AB1234)"
                  value={vehicleNumber}
                  onChange={(e) => setVehicleNumber(e.target.value.toUpperCase())}
                  onKeyDown={handleVehicleKeyPress}
                  disabled={isSearchingVehicle}
                  className="flex-1"
                />
                <Button
                  onClick={searchVehicle}
                  disabled={isSearchingVehicle || !vehicleNumber.trim()}
                  className="w-full sm:w-auto"
                >
                  {isSearchingVehicle ? <Loader2 className="h-4 w-4 animate-spin" /> : <Search className="h-4 w-4" />}
                  Search Vehicle
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Step 2: LR Entry */}
      {step === "lr" && selectedVehicle && (
        <>
          {/* Vehicle Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Truck className="h-5 w-5" />
                Vehicle Selected
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div>
                  <p className="text-muted-foreground">Registration Number</p>
                  <p className="font-medium">{selectedVehicle.registration_number}</p>
                </div>
                <div>
                  <p className="text-muted-foreground">Vehicle Type</p>
                  <p className="font-medium">{selectedVehicle.vehicle_type}</p>
                </div>
                <div>
                  <p className="text-muted-foreground">Status</p>
                  <Badge variant="outline">{selectedVehicle.current_status}</Badge>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* LR Entry */}
          <Card>
            <CardHeader>
              <CardTitle>Enter LR Numbers</CardTitle>
              <CardDescription>Enter LR numbers to receive parcels from this vehicle</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="lr-number">LR Number</Label>
                  <Input
                    id="lr-number"
                    placeholder="Enter LR number"
                    value={currentLR}
                    onChange={(e) => setCurrentLR(e.target.value)}
                    onKeyDown={handleLRKeyPress}
                    disabled={isValidating}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="quantity">Received Quantity</Label>
                  <Input
                    id="quantity"
                    type="number"
                    min="1"
                    placeholder="1"
                    value={currentQuantity}
                    onChange={(e) => setCurrentQuantity(e.target.value)}
                    disabled={isValidating}
                  />
                </div>
                <div className="space-y-2">
                  <Label>&nbsp;</Label>
                  <Button
                    onClick={addLREntry}
                    disabled={isValidating || !currentLR.trim()}
                    className="w-full"
                  >
                    {isValidating ? <Loader2 className="h-4 w-4 animate-spin" /> : <Plus className="h-4 w-4" />}
                    Add LR
                  </Button>
                </div>
              </div>

              {lrEntries.length > 0 && (
                <div className="space-y-2">
                  <Label>LR Numbers to Receive ({lrEntries.length})</Label>
                  <ScrollArea className="h-[200px] rounded-md border">
                    <div className="p-4 space-y-2">
                      {lrEntries.map((entry, index) => (
                        <div key={index} className="flex justify-between items-center py-2 border-b last:border-0">
                          <div className="flex-1">
                            <div className="flex items-center gap-2">
                              <p className="font-medium">{entry.lrNumber}</p>
                              {!entry.isValid && (
                                <Badge variant="destructive" className="text-xs">Invalid</Badge>
                              )}
                            </div>
                            {entry.parcelDetails && (
                              <div className="text-xs text-muted-foreground mt-1">
                                <p>{entry.parcelDetails.sender_name} → {entry.parcelDetails.recipient_name}</p>
                                <p>{entry.parcelDetails.sender_branch} → {entry.parcelDetails.delivery_branch}</p>
                                <p>Status: {entry.parcelDetails.current_status}</p>
                              </div>
                            )}
                            {entry.validationMessage && (
                              <p className="text-xs text-red-600 mt-1">{entry.validationMessage}</p>
                            )}
                          </div>
                          <div className="flex items-center space-x-2">
                            <div className="flex items-center space-x-1 bg-muted rounded-md p-1">
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-6 w-6 p-0"
                                onClick={() => adjustQuantity(index, -1)}
                                disabled={entry.quantity <= 1}
                              >
                                <Minus className="h-3 w-3" />
                              </Button>
                              <span className="w-8 text-center text-sm">{entry.quantity}</span>
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-6 w-6 p-0"
                                onClick={() => adjustQuantity(index, 1)}
                              >
                                <Plus className="h-3 w-3" />
                              </Button>
                            </div>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-6 w-6 p-0 text-red-600 hover:text-red-700"
                              onClick={() => removeLREntry(index)}
                            >
                              <Trash2 className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </ScrollArea>
                </div>
              )}

              {lrEntries.length > 0 && (
                <div className="flex justify-between items-center pt-4">
                  <Button variant="outline" onClick={() => setStep("vehicle")}>
                    Back to Vehicle
                  </Button>
                  <Button onClick={() => setStep("confirmation")}>
                    Proceed to Receive ({lrEntries.filter(e => e.isValid).length} valid)
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </>
      )}

      {/* Step 3: Confirmation */}
      {step === "confirmation" && selectedVehicle && (
        <Card>
          <CardHeader>
            <CardTitle>Confirm Receiving Operation</CardTitle>
            <CardDescription>Review the details before completing the receiving operation</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Debug Information */}
            <div className="bg-gray-50 p-3 rounded-lg border text-xs">
              <p><strong>Debug Info:</strong></p>
              <p>Selected Vehicle: {selectedVehicle ? '✅ Set' : '❌ Missing'}</p>
              <p>User Branch ID: {userBranchId ? '✅ Set' : '❌ Missing'} (Value: {userBranchId})</p>
              <p>LocalStorage Branch ID: {localStorage.getItem('userBranchId') ? '✅ Set' : '❌ Missing'} (Value: {localStorage.getItem('userBranchId')})</p>
              <p>LR Entries: {lrEntries.length} total, {lrEntries.filter(e => e.isValid).length} valid</p>
            </div>
          <CardContent className="space-y-4">
            <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div>
                  <p className="text-blue-700 font-medium">Vehicle</p>
                  <p className="text-blue-800">{selectedVehicle.registration_number}</p>
                </div>
                <div>
                  <p className="text-blue-700 font-medium">Total LR Numbers</p>
                  <p className="text-blue-800">{lrEntries.filter(e => e.isValid).length}</p>
                </div>
                <div>
                  <p className="text-blue-700 font-medium">Total Items</p>
                  <p className="text-blue-800">{lrEntries.filter(e => e.isValid).reduce((sum, e) => sum + e.quantity, 0)}</p>
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <Label>LR Numbers to Receive</Label>
              <div className="max-h-[200px] overflow-y-auto border rounded-md p-3 space-y-2">
                {lrEntries.filter(e => e.isValid).map((entry, index) => (
                  <div key={index} className="flex justify-between items-center text-sm">
                    <span className="font-medium">{entry.lrNumber}</span>
                    <span className="text-muted-foreground">{entry.quantity} items</span>
                  </div>
                ))}
              </div>
            </div>

            <div className="flex justify-between">
              <Button variant="outline" onClick={() => setStep("lr")}>
                Back to LR Entry
              </Button>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  onClick={() => {
                    console.log("🔍 Debug button clicked - Current state:")
                    console.log("  - selectedVehicle:", selectedVehicle)
                    console.log("  - userBranchId:", userBranchId)
                    console.log("  - lrEntries:", lrEntries)
                    console.log("  - step:", step)
                    console.log("  - localStorage userBranchId:", localStorage.getItem('userBranchId'))
                  }}
                >
                  Debug State
                </Button>
                <Button onClick={submitReceiving} disabled={isSubmitting}>
                  {isSubmitting ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : null}
                  Complete Receiving
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Step 4: Completion */}
      {step === "complete" && (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center space-y-4">
              <div className="mx-auto w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                <CheckCircle className="h-6 w-6 text-green-600" />
              </div>
              <div>
                <h3 className="text-lg font-semibold">Receiving Completed Successfully!</h3>
                <p className="text-muted-foreground">All parcels have been processed and recorded in the system.</p>
              </div>
              <Button onClick={resetForm} className="w-full">
                Start New Receiving Operation
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Completion Dialog */}
      {showCompletionDialog && completionData && (
        <ReceivingCompletionDialog
          isOpen={showCompletionDialog}
          onClose={handleCompletionClose}
          completionData={completionData}
        />
      )}
    </div>
  )
}
