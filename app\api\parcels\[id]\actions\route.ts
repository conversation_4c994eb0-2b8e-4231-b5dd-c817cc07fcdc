import { NextRequest, NextResponse } from "next/server";
import { supabase } from "@/lib/supabase";

// GET /api/parcels/[id]/actions
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    const id = parseInt(params.id);

    if (isNaN(id)) {
      return NextResponse.json({ error: "Invalid parcel ID" }, { status: 400 });
    }

    console.log(`Fetching parcel actions for parcel ${id}`);

    // Get all actions for the parcel from parcel_actions table
    const { data: actions, error: actionsError } = await supabase
      .from("parcel_actions")
      .select(`
        action_id,
        action_type,
        action_timestamp,
        branch_id,
        location_name,
        vehicle_id,
        loading_type,
        destination_branch_id,
        quantity,
        quantity_loaded,
        quantity_received,
        remarks,
        created_by
      `)
      .eq("parcel_id", id)
      .order("action_timestamp", { ascending: true });

    if (actionsError) {
      console.error("Error fetching parcel actions:", actionsError);
      console.error("Error details:", JSON.stringify(actionsError, null, 2));
      return NextResponse.json(
        {
          error: "Failed to fetch parcel actions",
          details: actionsError.message,
        },
        { status: 500 },
      );
    }

    console.log(`Found ${actions?.length || 0} actions for parcel ${id}`);

    // Get unique branch IDs and vehicle IDs to fetch their names
    const branchIds = [
      ...new Set(
        actions?.flatMap((a) =>
          [a.branch_id, a.destination_branch_id].filter(Boolean)
        ) || [],
      ),
    ];
    const vehicleIds = [
      ...new Set(actions?.map((a) => a.vehicle_id).filter(Boolean) || []),
    ];

    // Fetch branch names
    const { data: branches } = await supabase
      .from("branches")
      .select("branch_id, name")
      .in("branch_id", branchIds);

    // Fetch vehicle registrations
    const { data: vehicles } = await supabase
      .from("vehicles")
      .select("vehicle_id, registration_number")
      .in("vehicle_id", vehicleIds);

    // Create lookup maps
    const branchMap = new Map(
      branches?.map((b) => [b.branch_id, b.name]) || [],
    );
    const vehicleMap = new Map(
      vehicles?.map((v) => [v.vehicle_id, v.registration_number]) || [],
    );

    // Process actions to create timeline and detailed views
    const processedActions = actions?.map((action) => ({
      ...action,
      branch_name: branchMap.get(action.branch_id) || action.location_name,
      destination_branch_name: branchMap.get(action.destination_branch_id),
      vehicle_registration: vehicleMap.get(action.vehicle_id),
    })) || [];

    // Create timeline view (only status changes)
    const timelineActions = processedActions.filter((action) => {
      // Only include actions that represent actual status changes
      return ["Booked", "Loaded", "Received", "Delivered"].includes(
        action.action_type,
      );
    });

    // For timeline, we need to determine if this action actually changed the status
    // This logic should match the business rules for when status changes occur
    const statusChangeActions = [];

    for (const action of timelineActions) {
      if (action.action_type === "Booked") {
        // Always include booking
        statusChangeActions.push(action);
      } else if (action.action_type === "Loaded") {
        // Include loading actions
        statusChangeActions.push(action);
      } else if (action.action_type === "Received") {
        // Only include if this was the final receive that changed status to "Received"
        // This would need to be determined by checking if total_quantity_at_branch equals parcel total
        statusChangeActions.push(action);
      } else if (action.action_type === "Delivered") {
        // Always include delivery
        statusChangeActions.push(action);
      }
    }

    return NextResponse.json({
      timeline: statusChangeActions,
      detailed: processedActions,
      total_actions: processedActions.length,
    });
  } catch (error: any) {
    console.error(`Error in GET /api/parcels/${params.id}/actions:`, error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 },
    );
  }
}
