"use client"

import { FlexibleVehicleReceivingPanel } from "@/components/flexible-vehicle-receiving-panel"

export default function FlexibleReceivingPage() {
  return (
    <div className="container mx-auto py-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold">Flexible Vehicle Receiving</h1>
        <p className="text-gray-600 mt-2">
          Receive parcels from any vehicle without loading chart requirements. 
          This system allows maximum flexibility for receiving operations.
        </p>
      </div>
      
      <FlexibleVehicleReceivingPanel />
      
      <div className="mt-8 p-4 bg-blue-50 rounded-lg border border-blue-200">
        <h2 className="text-lg font-medium text-blue-800 mb-2">Key Features</h2>
        <ul className="text-blue-700 space-y-1 text-sm">
          <li>• <strong>No Loading Chart Dependency:</strong> Receive parcels without requiring loading charts</li>
          <li>• <strong>Flexible Vehicle Validation:</strong> Only checks if vehicle exists and is active</li>
          <li>• <strong>Any Parcel Status:</strong> Accept parcels in Booked, Loaded, or Received status</li>
          <li>• <strong>Quantity Management:</strong> Adjust item counts with increment/decrement controls</li>
          <li>• <strong>Smart Duplicate Handling:</strong> Automatically merge quantities for duplicate LR entries</li>
          <li>• <strong>Status Intelligence:</strong> Automatically determines parcel status based on destination and quantities</li>
          <li>• <strong>Comprehensive Reporting:</strong> Generate detailed receiving reports with PDF and WhatsApp sharing</li>
          <li>• <strong>Lost Item Recovery:</strong> Support for receiving items that were lost and later found</li>
        </ul>
      </div>
      
      <div className="mt-4 p-4 bg-yellow-50 rounded-lg border border-yellow-200">
        <h2 className="text-lg font-medium text-yellow-800 mb-2">Important Notes</h2>
        <ul className="text-yellow-700 space-y-1 text-sm">
          <li>• This system bypasses traditional loading chart validation for maximum flexibility</li>
          <li>• Parcels can be received at any branch, not just their destination</li>
          <li>• The system tracks all receiving actions for audit purposes</li>
          <li>• Status updates are automatic based on destination branch and item quantities</li>
          <li>• Warnings are shown when receiving more items than originally in the parcel</li>
        </ul>
      </div>
    </div>
  )
}
