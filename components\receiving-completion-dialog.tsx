"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { ScrollArea } from "@/components/ui/scroll-area"
import { 
  CheckCircle, 
  Download, 
  Share2, 
  PackageOpen, 
  Truck, 
  MapPin,
  FileText,
  Loader2,
  AlertTriangle
} from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { WhatsAppShareDialog } from "./whatsapp-share-dialog"

interface ReceivingCompletionDialogProps {
  isOpen: boolean
  onClose: () => void
  completionData: {
    vehicle: {
      registration_number: string
      vehicle_type: string
      make_model: string
    }
    parcels: Array<{
      lrNumber: string
      quantity: number
      isValid: boolean
      parcelDetails?: {
        sender_name: string
        recipient_name: string
        sender_branch: string
        delivery_branch: string
        current_status: string
      }
    }>
    results: Array<{
      lr_number: string
      success: boolean
      received_quantity: number
      total_received: number
      original_quantity: number
      new_status: string
      warning?: string
      parcel_details?: {
        sender_name: string
        recipient_name: string
        sender_branch: string
        delivery_branch: string
      }
    }>
    summary: {
      total_parcels: number
      successful: number
      failed: number
    }
    receiving_date: string
  }
}

export function ReceivingCompletionDialog({
  isOpen,
  onClose,
  completionData
}: ReceivingCompletionDialogProps) {
  const { toast } = useToast()
  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false)
  const [showWhatsAppDialog, setShowWhatsAppDialog] = useState(false)

  const handleDownloadPDF = async () => {
    setIsGeneratingPDF(true)
    
    try {
      // Placeholder for PDF generation
      await new Promise(resolve => setTimeout(resolve, 2000)) // Simulate API call
      
      // For now, create a simple text file as placeholder
      const reportContent = generateReportContent()
      const blob = new Blob([reportContent], { type: 'text/plain' })
      const url = URL.createObjectURL(blob)
      
      const link = document.createElement('a')
      link.href = url
      link.download = `Flexible-Receiving-Report-${new Date().toISOString().split('T')[0]}.txt`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)
      
      toast({
        title: "PDF Downloaded",
        description: "Receiving report has been downloaded successfully.",
      })
    } catch (error) {
      toast({
        title: "Download Failed",
        description: "Failed to generate PDF report. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsGeneratingPDF(false)
    }
  }

  const generateReportContent = () => {
    return `
FLEXIBLE RECEIVING REPORT
=========================

Date: ${new Date(completionData.receiving_date).toLocaleDateString()}
Time: ${new Date(completionData.receiving_date).toLocaleTimeString()}

VEHICLE INFORMATION
-------------------
Registration: ${completionData.vehicle.registration_number}
Type: ${completionData.vehicle.vehicle_type}
Model: ${completionData.vehicle.make_model}

RECEIVING SUMMARY
-----------------
Total Parcels: ${completionData.summary.total_parcels}
Successful: ${completionData.summary.successful}
Failed: ${completionData.summary.failed}

RECEIVED PARCELS
----------------
${completionData.results.map(result =>
  `LR: ${result.lr_number} | Status: ${result.success ? 'Success' : 'Failed'} | Received: ${result.received_quantity} | New Status: ${result.new_status}${result.warning ? ' | Warning: ' + result.warning : ''}`
).join('\n')}

Generated by KPN Parcel Service - Flexible Receiving System
    `.trim()
  }

  const handleShareWhatsApp = () => {
    setShowWhatsAppDialog(true)
  }

  const handleWhatsAppShare = async (phoneNumbers: string[], message: string) => {
    try {
      // Placeholder for WhatsApp sharing
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      toast({
        title: "Shared Successfully",
        description: `Receiving report shared with ${phoneNumbers.length} contact(s).`,
      })
      
      setShowWhatsAppDialog(false)
    } catch (error) {
      toast({
        title: "Share Failed",
        description: "Failed to share report. Please try again.",
        variant: "destructive",
      })
    }
  }

  const hasWarnings = completionData.results.some(r => r.warning)
  const hasFailures = completionData.summary.failed > 0

  return (
    <>
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
          <DialogHeader>
            <div className="flex items-center gap-3">
              <CheckCircle className="h-8 w-8 text-green-600" />
              <div>
                <DialogTitle className="text-xl">Receiving Operation Completed!</DialogTitle>
                <p className="text-sm text-muted-foreground mt-1">
                  {completionData.summary.successful} of {completionData.summary.total_parcels} parcels received successfully
                </p>
              </div>
            </div>
          </DialogHeader>

          <ScrollArea className="max-h-[60vh] pr-4">
            <div className="space-y-6">
              {/* Warnings and Failures */}
              {(hasWarnings || hasFailures) && (
                <Card className="border-orange-200 bg-orange-50">
                  <CardContent className="pt-4">
                    <div className="flex items-center gap-2 text-orange-800">
                      <AlertTriangle className="h-4 w-4" />
                      <div>
                        {hasFailures && (
                          <p className="text-sm font-medium">
                            {completionData.summary.failed} parcel(s) failed to process.
                          </p>
                        )}
                        {hasWarnings && (
                          <p className="text-sm font-medium">
                            Some parcels have warnings that require attention.
                          </p>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Summary Cards */}
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-base flex items-center gap-2">
                      <Truck className="h-4 w-4" />
                      Vehicle Details
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <div>
                      <p className="text-sm text-muted-foreground">Registration</p>
                      <p className="font-medium">{completionData.vehicle.registration_number}</p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Type</p>
                      <p className="font-medium">{completionData.vehicle.vehicle_type}</p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Model</p>
                      <p className="font-medium">{completionData.vehicle.make_model}</p>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-base flex items-center gap-2">
                      <PackageOpen className="h-4 w-4" />
                      Summary
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <div>
                      <p className="text-sm text-muted-foreground">Total Parcels</p>
                      <p className="font-medium">{completionData.summary.total_parcels}</p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Successful</p>
                      <p className="font-medium text-green-600">{completionData.summary.successful}</p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Failed</p>
                      <p className="font-medium text-red-600">{completionData.summary.failed}</p>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-base flex items-center gap-2">
                      <MapPin className="h-4 w-4" />
                      Operation Details
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <div>
                      <p className="text-sm text-muted-foreground">Date</p>
                      <p className="font-medium">{new Date(completionData.receiving_date).toLocaleDateString()}</p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Time</p>
                      <p className="font-medium">{new Date(completionData.receiving_date).toLocaleTimeString()}</p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Type</p>
                      <p className="font-medium">Flexible Receiving</p>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Received Parcels */}
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-base flex items-center gap-2">
                    <PackageOpen className="h-4 w-4" />
                    Processing Results ({completionData.results.length})
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {completionData.results.map((result, index) => (
                      <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex-1">
                          <div className="flex items-center gap-2">
                            <p className="font-medium">{result.lr_number}</p>
                            <Badge variant={result.success ? "default" : "destructive"}>
                              {result.success ? "Success" : "Failed"}
                            </Badge>
                          </div>
                          {result.parcel_details && (
                            <div className="text-sm text-muted-foreground mt-1">
                              <p>{result.parcel_details.sender_name} → {result.parcel_details.recipient_name}</p>
                              <p>{result.parcel_details.sender_branch} → {result.parcel_details.delivery_branch}</p>
                            </div>
                          )}
                          {result.warning && (
                            <div className="mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded text-xs text-yellow-800">
                              <strong>Warning:</strong> {result.warning}
                            </div>
                          )}
                        </div>
                        <div className="text-right">
                          <p className="text-sm">
                            <span className="text-muted-foreground">Received:</span> {result.received_quantity}
                          </p>
                          <p className="text-sm">
                            <span className="text-muted-foreground">Total:</span> {result.total_received}
                          </p>
                          <p className="text-sm">
                            <span className="text-muted-foreground">Status:</span> {result.new_status}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </ScrollArea>

          <Separator />

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-3">
            <Button
              onClick={handleDownloadPDF}
              disabled={isGeneratingPDF}
              className="flex-1"
              variant="outline"
            >
              {isGeneratingPDF ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Download className="h-4 w-4 mr-2" />
              )}
              {isGeneratingPDF ? "Generating..." : "Download PDF Report"}
            </Button>
            
            <Button
              onClick={handleShareWhatsApp}
              className="flex-1"
              variant="outline"
            >
              <Share2 className="h-4 w-4 mr-2" />
              Share via WhatsApp
            </Button>
            
            <Button onClick={onClose} className="flex-1">
              <FileText className="h-4 w-4 mr-2" />
              Complete
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      <WhatsAppShareDialog
        open={showWhatsAppDialog}
        onOpenChange={setShowWhatsAppDialog}
        onShare={handleWhatsAppShare}
        reportType="Flexible Receiving Report"
        reportNumber={`${new Date(completionData.receiving_date).toISOString().split('T')[0]}`}
      />
    </>
  )
}
