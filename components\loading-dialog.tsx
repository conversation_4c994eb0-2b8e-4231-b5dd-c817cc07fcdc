"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import { useToast } from "@/hooks/use-toast"
import { Loader2, Plus, Minus, Check, AlertCircle, Truck, Search, X } from "lucide-react"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"

interface LoadingDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  memo: any
  onLoadComplete: () => void
}

interface LREntry {
  lrNumber: string
  quantity: number
  isValid: boolean
  validationMessage?: string
  destination?: string
}

export function LoadingDialog({ open, onOpenChange, memo, onLoadComplete }: LoadingDialogProps) {
  const { toast } = useToast()
  const [destinationBranch, setDestinationBranch] = useState<string>("")
  const [branches, setBranches] = useState<any[]>([])
  const [lrEntries, setLrEntries] = useState<LREntry[]>([])
  const [currentLR, setCurrentLR] = useState("")
  const [currentQuantity, setCurrentQuantity] = useState("1")
  const [isValidating, setIsValidating] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [loadingChartId, setLoadingChartId] = useState<string | null>(null)
  
  // Fetch branches on component mount
  useEffect(() => {
    if (open) {
      fetchBranches()
      // Reset state when dialog opens
      setLrEntries([])
      setCurrentLR("")
      setCurrentQuantity("1")
      setDestinationBranch("")
      setError(null)
      setLoadingChartId(null)
    }
  }, [open])

  // Fetch branches from API
  const fetchBranches = async () => {
    try {
      const response = await fetch('/api/branches')
      const data = await response.json()

      if (response.ok) {
        setBranches(data.branches || [])
      } else {
        console.error('Error fetching branches:', data.error)
        setError('Failed to fetch branches')
      }
    } catch (error: any) {
      console.error('Error fetching branches:', error)
      setError('Failed to fetch branches')
    }
  }

  // Validate LR number against the database
  const validateLR = async (lrNumber: string): Promise<LREntry> => {
    setIsValidating(true)
    
    try {
      const response = await fetch(`/api/parcels/validate-lr?lr_number=${lrNumber}`)
      const data = await response.json()

      if (response.ok && data.valid) {
        return {
          lrNumber,
          quantity: parseInt(currentQuantity) || 1,
          isValid: true,
          destination: data.destination_branch?.name || 'Unknown'
        }
      } else {
        return {
          lrNumber,
          quantity: parseInt(currentQuantity) || 1,
          isValid: false,
          validationMessage: data.message || 'Invalid LR number'
        }
      }
    } catch (error: any) {
      console.error('Error validating LR:', error)
      return {
        lrNumber,
        quantity: parseInt(currentQuantity) || 1,
        isValid: false,
        validationMessage: 'Failed to validate LR number'
      }
    } finally {
      setIsValidating(false)
    }
  }

  // Add LR entry to the list
  const addLREntry = async () => {
    if (!currentLR.trim()) {
      toast({
        title: "Missing Information",
        description: "Please enter an LR number",
        variant: "destructive",
      })
      return
    }

    // Check if LR already exists in the list
    if (lrEntries.some(entry => entry.lrNumber === currentLR.trim())) {
      toast({
        title: "Duplicate LR",
        description: "This LR number is already in the list",
        variant: "destructive",
      })
      return
    }

    const validatedEntry = await validateLR(currentLR.trim())
    
    setLrEntries(prev => [...prev, validatedEntry])
    setCurrentLR("")
    setCurrentQuantity("1")
  }

  // Remove LR entry from the list
  const removeLREntry = (index: number) => {
    setLrEntries(prev => prev.filter((_, i) => i !== index))
  }

  // Generate loading chart
  const generateLoadingChart = async () => {
    if (!destinationBranch) {
      toast({
        title: "Missing Information",
        description: "Please select a destination branch",
        variant: "destructive",
      })
      return
    }

    if (lrEntries.length === 0) {
      toast({
        title: "Missing Information",
        description: "Please add at least one LR number",
        variant: "destructive",
      })
      return
    }

    // Check if all LRs are valid
    const invalidLRs = lrEntries.filter(entry => !entry.isValid)
    if (invalidLRs.length > 0) {
      toast({
        title: "Invalid LR Numbers",
        description: `There are ${invalidLRs.length} invalid LR numbers in the list`,
        variant: "destructive",
      })
      return
    }

    setIsSubmitting(true)
    setError(null)

    try {
      const response = await fetch('/api/loading-charts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          memo_id: memo.memo_id,
          vehicle_id: memo.vehicle_id,
          destination_branch_id: parseInt(destinationBranch),
          lr_entries: lrEntries.map(entry => ({
            lr_number: entry.lrNumber,
            quantity: entry.quantity
          }))
        }),
      })

      const data = await response.json()

      if (response.ok) {
        setLoadingChartId(data.chart_id)
        toast({
          title: "Loading Chart Created",
          description: `Successfully created loading chart #${data.chart_number}`,
        })
      } else {
        setError(data.error || 'Failed to create loading chart')
        toast({
          title: "Error",
          description: data.error || "Failed to create loading chart",
          variant: "destructive",
        })
      }
    } catch (error: any) {
      console.error('Error creating loading chart:', error)
      setError('Failed to create loading chart')
      toast({
        title: "Error",
        description: "Failed to create loading chart. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  // Finalize loading and close dialog
  const finalizeLoading = () => {
    onLoadComplete()
    onOpenChange(false)
  }

  // Handle key press in LR input
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      addLREntry()
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-3xl">
        <DialogHeader>
          <DialogTitle>Load Parcels</DialogTitle>
          <DialogDescription>
            Load parcels onto vehicle {memo?.vehicle?.registration_number || 'N/A'} for memo {memo?.memo_number || 'N/A'}
          </DialogDescription>
        </DialogHeader>

        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {!loadingChartId ? (
          <div className="space-y-4">
            {/* Destination Branch Selection */}
            <div className="space-y-2">
              <Label htmlFor="destination-branch">Destination Branch</Label>
              <Select value={destinationBranch} onValueChange={setDestinationBranch}>
                <SelectTrigger id="destination-branch">
                  <SelectValue placeholder="Select destination branch" />
                </SelectTrigger>
                <SelectContent>
                  {branches.map((branch) => (
                    <SelectItem key={branch.branch_id} value={branch.branch_id.toString()}>
                      {branch.name} ({branch.code})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* LR Entry Form */}
            <div className="space-y-2">
              <Label>Enter LR Numbers</Label>
              <div className="flex space-x-2">
                <Input
                  placeholder="Enter LR number"
                  value={currentLR}
                  onChange={(e) => setCurrentLR(e.target.value)}
                  onKeyDown={handleKeyPress}
                  disabled={isValidating}
                />
                <Input
                  type="number"
                  placeholder="Qty"
                  value={currentQuantity}
                  onChange={(e) => setCurrentQuantity(e.target.value)}
                  className="w-20"
                  min="1"
                  disabled={isValidating}
                />
                <Button onClick={addLREntry} disabled={isValidating || !currentLR.trim()}>
                  {isValidating ? <Loader2 className="h-4 w-4 animate-spin" /> : <Plus className="h-4 w-4" />}
                </Button>
              </div>
            </div>

            {/* LR Entries List */}
            <div className="space-y-2">
              <Label>LR Numbers to Load</Label>
              <ScrollArea className="h-[200px] rounded-md border">
                <div className="p-4 space-y-2">
                  {lrEntries.length === 0 ? (
                    <div className="text-center text-muted-foreground py-8">
                      No LR numbers added yet
                    </div>
                  ) : (
                    lrEntries.map((entry, index) => (
                      <Card key={index} className="p-3 flex items-center justify-between">
                        <div>
                          <div className="flex items-center">
                            <span className="font-medium">{entry.lrNumber}</span>
                            <Badge 
                              variant={entry.isValid ? "default" : "destructive"}
                              className="ml-2"
                            >
                              {entry.isValid ? "Valid" : "Invalid"}
                            </Badge>
                            <Badge variant="outline" className="ml-2">
                              Qty: {entry.quantity}
                            </Badge>
                          </div>
                          {entry.isValid ? (
                            <p className="text-xs text-muted-foreground">
                              Destination: {entry.destination}
                            </p>
                          ) : (
                            <p className="text-xs text-destructive">
                              {entry.validationMessage}
                            </p>
                          )}
                        </div>
                        <Button 
                          variant="ghost" 
                          size="sm" 
                          onClick={() => removeLREntry(index)}
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </Card>
                    ))
                  )}
                </div>
              </ScrollArea>
            </div>

            <Separator />

            {/* Summary */}
            <div className="flex justify-between items-center">
              <div>
                <p className="text-sm font-medium">Total LRs: {lrEntries.length}</p>
                <p className="text-sm text-muted-foreground">
                  Valid: {lrEntries.filter(entry => entry.isValid).length} | 
                  Invalid: {lrEntries.filter(entry => !entry.isValid).length}
                </p>
              </div>
              <Button 
                onClick={generateLoadingChart} 
                disabled={isSubmitting || lrEntries.length === 0 || !destinationBranch}
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Processing...
                  </>
                ) : (
                  <>
                    Generate Loading Chart
                  </>
                )}
              </Button>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            <Alert>
              <Check className="h-4 w-4" />
              <AlertTitle>Loading Chart Created</AlertTitle>
              <AlertDescription>
                The loading chart has been successfully created. You can now print it or send it via WhatsApp.
              </AlertDescription>
            </Alert>

            <div className="flex justify-center">
              <div className="bg-muted p-6 rounded-lg w-full max-w-md text-center">
                <Truck className="h-12 w-12 mx-auto mb-4 text-primary" />
                <h3 className="text-lg font-bold mb-2">Loading Chart #{loadingChartId}</h3>
                <p className="text-sm text-muted-foreground mb-4">
                  Vehicle: {memo?.vehicle?.registration_number || 'N/A'}<br />
                  Total LRs: {lrEntries.length}<br />
                  Destination: {branches.find(b => b.branch_id.toString() === destinationBranch)?.name || 'N/A'}
                </p>
                <div className="flex justify-center space-x-2">
                  <Button variant="outline" onClick={() => {
                    // Format the WhatsApp message
                    let message = `Loading Chart for Vehicle ${memo?.vehicle?.registration_number || 'N/A'}\n` +
                      `Memo: ${memo?.memo_number || 'N/A'}\n` +
                      `Destination: ${branches.find(b => b.branch_id.toString() === destinationBranch)?.name || 'N/A'}\n\n` +
                      `LR Numbers:\n`;
                    
                    lrEntries.forEach((entry, index) => {
                      message += `${index + 1}. ${entry.lrNumber} (Qty: ${entry.quantity})\n`;
                    });
                    
                    // Encode the message for WhatsApp
                    const encodedMessage = encodeURIComponent(message);
                    
                    // Open WhatsApp with the pre-filled message
                    window.open(`https://wa.me/?text=${encodedMessage}`, '_blank');
                  }}>
                    Send via WhatsApp
                  </Button>
                  <Button onClick={finalizeLoading}>
                    Complete
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
